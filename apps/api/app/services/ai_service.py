# app/services/ai_service.py
"""
Enhanced AI service for bill processing using OpenAI GPT-4.

This service provides comprehensive AI analysis of bills including:
- Plain English summaries
- Support/oppose/amend reasons
- Message templates
- Categorization tags
"""

import openai
import asyncio
import json
import logging
import os
import time
from typing import Dict, List, Any, Optional
from tenacity import retry, stop_after_attempt, wait_exponential
from app.core.config import get_settings
# from app.core.ai_models import get_model_config, TaskType, SectionRouter, EXTRACTION_SCHEMA, SUMMARY_SCHEMA
# from app.services.span_retriever import SpanRetriever, create_evidence_prompt
from app.utils.ai_tracking_decorator import track_openai_response, AIUsageContext
from app.services.span_grounded_validator import SpanGroundedValidator
from app.services.enhanced_span_retriever import EnhancedSpanRetriever
from app.services.enriched_analysis_service import EnrichedAnalysisService
from app.services.world_class_analysis_service import WorldClassAnalysisService
from app.services.balanced_analysis_service import BalancedAnalysisService
from app.services.simple_enriched_service import SimpleEnrichedService

logger = logging.getLogger(__name__)
settings = get_settings()


class AIService:
    """Enhanced AI service for comprehensive bill analysis using OpenAI GPT models"""

    def __init__(self):
        self.api_key = getattr(settings, 'OPENAI_API_KEY', None) or os.getenv('OPENAI_API_KEY')

        if not self.api_key:
            logger.warning("OPENAI_API_KEY not configured. AI processing features will be disabled.")
            self.enabled = False
        else:
            self.client = openai.AsyncOpenAI(api_key=self.api_key)
            self.enabled = True

            # Configure model based on tier for cost optimization
            self.model_tier = getattr(settings, 'AI_MODEL_TIER', 'dev')
            self.max_tokens = getattr(settings, 'AI_MAX_TOKENS_PER_REQUEST', 500)

            # Initialize span-grounded validator and enhanced retriever
            self.validator = SpanGroundedValidator()
            self.span_retriever = EnhancedSpanRetriever()
            self.enriched_analyzer = EnrichedAnalysisService(self)
            self.world_class_analyzer = WorldClassAnalysisService(self)
            self.balanced_analyzer = BalancedAnalysisService(self)
            self.simple_enriched = SimpleEnrichedService(self)

            if self.model_tier == "dev":
                self.model = "gpt-4o-mini"  # Much cheaper for development
                logger.info("OpenAI AI service initialized with GPT-4o-mini (development mode)")
            elif self.model_tier == "staging":
                self.model = "gpt-4o"
                logger.info("OpenAI AI service initialized with GPT-4o (staging mode)")
            else:  # production
                self.model = "gpt-4o"
                logger.info("OpenAI AI service initialized with GPT-4o (production mode)")

            logger.info(f"AI cost optimization: tier={self.model_tier}, model={self.model}, max_tokens={self.max_tokens}")

    async def analyze_bill_cost_optimized(self, bill_text: str, bill_metadata: Dict,
                                        source_index: List[Dict] = None) -> Dict[str, Any]:
        """Quality-first cost optimization - smart analysis based on content complexity."""
        if not self.enabled:
            raise RuntimeError("AI service is not enabled")

        try:
            # Intelligent content routing
            bill_title = bill_metadata.get('title', '')
            is_placeholder = any(phrase in bill_title.lower() for phrase in [
                'reserved for', 'placeholder', 'to be determined', 'tbd'
            ])

            if is_placeholder or len(bill_text.strip()) < 100:
                logger.info("🔍 Detected placeholder bill - using minimal processing")
                return await self._handle_placeholder_bill(bill_title, bill_text)

            # For real bills, use span-grounded analysis
            logger.info("🚀 Using SPAN-GROUNDED analysis for real bill content")

            # Extract evidence spans for each category
            spans = await self._extract_evidence_spans(bill_text, bill_metadata)

            # Validate span coverage
            if not self._validate_span_coverage(spans):
                logger.warning("Insufficient evidence spans - falling back to minimal analysis")
                return await self._handle_insufficient_evidence(bill_title, bill_text)

            # Use strict span-grounded prompt
            return await self._analyze_with_spans(spans, bill_metadata)

        except Exception as e:
            logger.error(f"Quality-optimized analysis failed: {e}")
            return {"success": False, "error": str(e)}

    async def analyze_bill_enriched(self, bill_text: str, bill_metadata: Dict,
                                  source_index: List[Dict] = None) -> Dict[str, Any]:
        """
        Comprehensive enriched analysis with two-pass system
        Stays under $0.30 budget while providing detailed analysis
        """
        if not self.enabled:
            raise RuntimeError("AI service is not enabled")

        try:
            bill_title = bill_metadata.get('title', 'Unknown Bill')
            logger.info(f"🚀 Starting enriched analysis for {bill_title}")

            # Check for placeholder bills
            if self._is_placeholder_bill(bill_title, bill_text):
                return await self._handle_placeholder_bill(bill_title, bill_text)

            # Extract enhanced evidence spans
            enhanced_result = self.span_retriever.extract_enhanced_spans(bill_text, bill_metadata)
            evidence_spans = enhanced_result.get('evidence', [])

            # Validate span coverage
            if len(evidence_spans) < 3:
                logger.warning("Insufficient evidence spans for enriched analysis")
                return await self._handle_insufficient_evidence(bill_title, bill_text)

            # Run simple enriched analysis (reliable)
            enriched_result = await self.simple_enriched.analyze_bill_simple_enriched(
                bill_text, bill_metadata, evidence_spans
            )

            if not enriched_result.get('success'):
                logger.error(f"Simple enriched analysis failed: {enriched_result.get('error')}")
                return enriched_result

            # Convert to standard format
            standard_result = self.simple_enriched.convert_to_standard_format(enriched_result)

            cost = enriched_result.get('cost', 0)
            logger.info(f"✅ Simple enriched analysis completed: ${cost:.4f}")

            return standard_result

        except Exception as e:
            logger.error(f"Enriched analysis failed: {e}")
            return {"success": False, "error": str(e)}

    async def analyze_bill_world_class(self, bill_text: str, bill_metadata: Dict,
                                     source_index: List[Dict] = None) -> Dict[str, Any]:
        """
        World-class analysis with bulletproof JSON and evidence-by-ID
        Production-ready with comprehensive validation and budget controls
        """
        if not self.enabled:
            raise RuntimeError("AI service is not enabled")

        try:
            bill_title = bill_metadata.get('title', 'Unknown Bill')
            logger.info(f"🌟 Starting world-class analysis for {bill_title}")

            # Check for placeholder bills
            if self._is_placeholder_bill(bill_title, bill_text):
                return await self._handle_placeholder_bill(bill_title, bill_text)

            # Extract enhanced evidence spans
            enhanced_result = self.span_retriever.extract_enhanced_spans(bill_text, bill_metadata)
            evidence_spans = enhanced_result.get('evidence', [])

            # Validate span coverage
            if len(evidence_spans) < 3:
                logger.warning("Insufficient evidence spans for world-class analysis")
                return await self._handle_insufficient_evidence(bill_title, bill_text)

            # Run world-class analysis
            world_class_result = await self.world_class_analyzer.analyze_bill_world_class(
                bill_text, bill_metadata, evidence_spans
            )

            if not world_class_result.get('success'):
                logger.error(f"World-class analysis failed: {world_class_result.get('error')}")
                return world_class_result

            # Apply validation gates
            analysis = world_class_result['analysis']

            # Convert for validation
            validation_payload = self._convert_world_class_for_validation(analysis)
            is_valid, validation_errors, validation_metrics = self.validator.validate_analysis(validation_payload)

            if not is_valid:
                logger.warning(f"World-class analysis failed validation: {validation_errors}")
                return {
                    "success": False,
                    "error": "World-class analysis failed quality validation",
                    "validation_errors": validation_errors,
                    "validation_metrics": validation_metrics,
                    "needs_human_review": True
                }

            # Convert to standard format for compatibility
            standard_result = self._convert_world_class_to_standard_format(world_class_result)

            logger.info(f"✅ World-class analysis completed: ${world_class_result['cost_breakdown']['total_cost']:.4f}")

            return standard_result

        except Exception as e:
            logger.error(f"World-class analysis failed: {e}")
            return {"success": False, "error": str(e)}

    async def analyze_bill_balanced(self, bill_text: str, bill_metadata: Dict,
                                  source_index: List[Dict] = None) -> Dict[str, Any]:
        """
        Balanced analysis: Premium quality where users see it, efficient background processing
        Target: $0.05-0.10 per bill with rich SEO detail
        """
        if not self.enabled:
            raise RuntimeError("AI service is not enabled")

        try:
            bill_title = bill_metadata.get('title', 'Unknown Bill')
            logger.info(f"🎯 Starting balanced analysis for {bill_title}")

            # Check for placeholder bills
            if self._is_placeholder_bill(bill_title, bill_text):
                return await self._handle_placeholder_bill(bill_title, bill_text)

            # Extract enhanced evidence spans
            enhanced_result = self.span_retriever.extract_enhanced_spans(bill_text, bill_metadata)
            evidence_spans = enhanced_result.get('evidence', [])

            # Validate span coverage
            if len(evidence_spans) < 3:
                logger.warning("Insufficient evidence spans for balanced analysis")
                return await self._handle_insufficient_evidence(bill_title, bill_text)

            # Run balanced analysis
            logger.info(f"🔍 Calling balanced analyzer with {len(evidence_spans)} evidence spans")
            balanced_result = await self.balanced_analyzer.analyze_bill_balanced(
                bill_text, bill_metadata, evidence_spans
            )
            logger.info(f"🔍 Balanced analyzer returned: success={balanced_result.get('success')}")

            if not balanced_result.get('success'):
                logger.error(f"Balanced analysis failed: {balanced_result.get('error')}")
                return balanced_result

            # Convert to standard format for compatibility
            standard_result = self._convert_balanced_to_standard_format(balanced_result)

            logger.info(f"✅ Balanced analysis completed: ${balanced_result['cost_breakdown']['total_cost']:.4f}")

            return standard_result

        except Exception as e:
            logger.error(f"Balanced analysis failed: {e}")
            return {"success": False, "error": str(e)}

    async def _handle_placeholder_bill(self, title: str, bill_text: str) -> Dict[str, Any]:
        """Handle placeholder bills with minimal processing."""
        logger.info("📝 Processing placeholder bill with minimal analysis")

        return {
            "success": True,
            "processing_level": "placeholder",
            "summary": {
                "tldr": f"This bill ({title}) is currently a placeholder and contains no legislative content.",
                "who_affected": "No specific groups affected as this is a placeholder",
                "why_matters": "This is a placeholder bill reserved for future legislation",
                "budget_impact": "No budget impact as this is a placeholder",
                "key_points": ["Placeholder bill with no current content"],
                "support_reasons": ["Placeholder for future legislative action"],
                "oppose_reasons": ["No content to oppose as this is a placeholder"],
                "amend_suggestions": ["Awaiting actual legislative content"]
            },
            "extraction": {"key_points": ["Placeholder bill"]},
            "cost_optimized": True,
            "_metadata": {
                "model": "rule_based",
                "cost": 0.0,
                "tokens": 0,
                "quality_validated": True
            }
        }

    def _validate_analysis_quality(self, result: Dict) -> bool:
        """Validate that the analysis meets quality standards."""
        required_fields = ["tldr", "who_affected", "why_matters", "budget_impact"]

        # Check all required fields exist
        for field in required_fields:
            if field not in result or not result[field]:
                logger.warning(f"Missing or empty required field: {field}")
                return False

        # Check for generic/placeholder content
        generic_phrases = [
            "citizens and government agencies",
            "legislative change affecting policy",
            "to be determined",
            "budget impact to be determined",
            "```json"
        ]

        for field in required_fields:
            content = str(result[field]).lower()
            for phrase in generic_phrases:
                if phrase in content:
                    logger.warning(f"Generic content detected in {field}: {phrase}")
                    return False

        # Check minimum content length
        if len(result.get("tldr", "")) < 20:
            logger.warning("TLDR too short")
            return False

        return True

    async def _retry_with_better_prompt(self, bill_text: str, bill_metadata: Dict) -> Dict[str, Any]:
        """Retry analysis with a more explicit prompt."""
        logger.info("🔄 Retrying analysis with improved prompt")

        retry_prompt = f"""
CRITICAL: You must return ONLY valid JSON. No markdown, no explanations, just JSON.

Analyze this bill: {bill_metadata.get('title', '')}

Bill content: {bill_text[:3000]}

Return this exact JSON structure:
{{
    "tldr": "Specific summary of what this bill actually does",
    "who_affected": "Specific groups mentioned in the bill",
    "why_matters": "Specific impact based on bill content",
    "budget_impact": "Specific financial details from the bill",
    "key_points": ["Actual provision 1", "Actual provision 2"],
    "support_reasons": ["Specific benefit 1", "Specific benefit 2"],
    "oppose_reasons": ["Specific concern 1", "Specific concern 2"],
    "amend_suggestions": ["Specific improvement 1", "Specific improvement 2"]
}}

RULES:
- Be specific to THIS bill
- No generic phrases
- If information isn't in the bill, say "Not specified in bill text"
- Use simple language
"""

        try:
            response = await self.client.chat.completions.create(
                model="gpt-4o",
                messages=[{"role": "user", "content": retry_prompt}],
                max_tokens=1200,
                temperature=0.1,  # Very low for consistency
                response_format={"type": "json_object"}
            )

            result = json.loads(response.choices[0].message.content)

            return {
                "success": True,
                "processing_level": "quality_retry",
                "summary": result,
                "extraction": {"key_points": result.get("key_points", [])},
                "cost_optimized": True,
                "_metadata": {
                    "model": "gpt-4o",
                    "cost": (response.usage.prompt_tokens * 0.0025 + response.usage.completion_tokens * 0.01) / 1000,
                    "tokens": response.usage.prompt_tokens + response.usage.completion_tokens,
                    "retry": True
                }
            }

        except Exception as e:
            logger.error(f"Retry analysis also failed: {e}")
            # Return a structured fallback
            return {
                "success": True,
                "processing_level": "fallback",
                "summary": {
                    "tldr": f"Analysis of {bill_metadata.get('title', 'this bill')} could not be completed due to technical issues",
                    "who_affected": "Analysis unavailable",
                    "why_matters": "Analysis unavailable",
                    "budget_impact": "Analysis unavailable",
                    "key_points": ["Analysis could not be completed"],
                    "support_reasons": ["Analysis unavailable"],
                    "oppose_reasons": ["Analysis unavailable"],
                    "amend_suggestions": ["Analysis unavailable"]
                },
                "extraction": {"key_points": ["Analysis failed"]},
                "cost_optimized": True,
                "_metadata": {"model": "fallback", "cost": 0.0, "tokens": 0}
            }





    def _rules_only_analysis(self, bill_text: str, bill_metadata: Dict) -> Dict[str, Any]:
        """Rules-based analysis for low-impact sections (no AI calls)."""

        # Simple regex-based extraction
        import re

        # Look for financial amounts
        money_matches = re.findall(r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?', bill_text)

        # Look for dates
        date_matches = re.findall(r'(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}', bill_text)

        # Look for requirements
        requirement_matches = re.findall(r'shall\s+\w+(?:\s+\w+){0,5}', bill_text, re.IGNORECASE)

        return {
            "success": True,
            "processing_level": "rules_only",
            "tldr": f"Administrative provision related to {bill_metadata.get('title', 'legislation')}",
            "who_affected": "Government agencies and administrative processes",
            "why_matters": "Ensures proper implementation and compliance",
            "budget_impact": money_matches[0] if money_matches else "No specific budget impact identified",
            "financial_amounts": money_matches,
            "key_dates": date_matches,
            "requirements": requirement_matches[:3],  # Top 3
            "cost_optimized": True,
            "ai_calls": 0
        }

    def _minimal_analysis(self, bill_text: str, bill_metadata: Dict) -> Dict[str, Any]:
        """Minimal analysis when no spans are found."""
        return {
            "success": True,
            "processing_level": "minimal",
            "tldr": f"Legislation titled: {bill_metadata.get('title', 'Unknown')}",
            "who_affected": "To be determined through detailed analysis",
            "why_matters": "Requires further review to determine impact",
            "budget_impact": "Budget impact analysis needed",
            "cost_optimized": True,
            "ai_calls": 0,
            "note": "Insufficient evidence spans found for detailed analysis"
        }

    async def generate_simple_summary(self, bill_text: str, metadata: dict) -> str:
        """Generate a simple, 8th grade reading level summary for bill cards"""
        prompt = f"""
        Create a simple, easy-to-understand summary of this bill for the general public.

        BILL: {metadata['title']}
        TEXT: {bill_text[:2000]}...

        Requirements:
        - Write at an 8th grade reading level
        - Use simple, everyday words
        - Keep it engaging and relatable
        - 2-3 sentences maximum
        - Focus on what this means for regular people
        - Avoid jargon, technical terms, or complex language

        Example style: "This bill would make prescription drugs cheaper for seniors by allowing Medicare to negotiate prices directly with drug companies. It could save families hundreds of dollars per year on medications."

        Write ONLY the summary, nothing else:
        """

        try:
            if not self.enabled:
                # Fallback for when AI is not available
                return f"This bill, {metadata['title']}, is currently being considered by Congress."

            response = await self._make_openai_request(
                messages=[
                    {"role": "system", "content": "You are an expert at explaining complex legislation in simple, clear language that anyone can understand."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=150,
                temperature=0.3
            )

            summary = response.choices[0].message.content.strip()
            logger.info(f"Generated simple summary: {len(summary)} characters")
            return summary

        except Exception as e:
            logger.error(f"Failed to generate simple summary: {e}")
            # Fallback to a basic summary
            return f"This bill, {metadata['title']}, is currently being considered by Congress."

    async def process_bill_complete(self, bill_text: str, bill_metadata: dict) -> dict:
        """
        Generate all AI content for a bill

        Returns:
            {
                'ai_summary': str,
                'support_reasons': List[str],
                'oppose_reasons': List[str],
                'amend_reasons': List[str],
                'message_templates': dict,
                'tags': List[str]
            }
        """
        if not self.enabled:
            logger.warning("AI service is not enabled. Returning fallback data.")
            return self._get_fallback_ai_data(bill_metadata)

        # Truncate text if too long for AI model (OpenAI has token limits)
        if len(bill_text) > 50000:  # Reduced limit to avoid rate limiting
            # Keep the beginning (title, summary) and a substantial portion of the bill
            bill_text = bill_text[:50000] + "\n\n... [text truncated for processing - analysis based on first 50,000 characters]"
            logger.info(f"Truncated bill text to 50,000 characters to avoid rate limits")

        # Sequential processing with delays to avoid rate limits
        logger.info("Starting complete AI analysis with rate limiting...")
        logger.info(f"Processing bill: {bill_metadata.get('title', 'Unknown')}")
        logger.info(f"Bill text length: {len(bill_text)} characters")

        try:
            # Step 1: Generate structured summary
            logger.info("Step 1: Generating structured AI summary...")
            structured_summary = await self._generate_summary(bill_text, bill_metadata)
            logger.info(f"Structured summary generated with {len(structured_summary)} sections")

            # Small delay to avoid rate limits
            await asyncio.sleep(1)

            # Step 2: Generate support reasons
            logger.info("Step 2: Generating support reasons...")
            support_reasons = await self._generate_support_reasons(bill_text, bill_metadata)
            logger.info(f"Support reasons generated: {len(support_reasons)} reasons")

            # Small delay to avoid rate limits
            await asyncio.sleep(1)

            # Step 3: Generate oppose reasons
            logger.info("Step 3: Generating oppose reasons...")
            oppose_reasons = await self._generate_oppose_reasons(bill_text, bill_metadata)
            logger.info(f"Oppose reasons generated: {len(oppose_reasons)} reasons")

            # Small delay to avoid rate limits
            await asyncio.sleep(1)

            # Step 4: Generate amend reasons
            logger.info("Step 4: Generating amend reasons...")
            amend_reasons = await self._generate_amend_reasons(bill_text, bill_metadata)
            logger.info(f"Amend reasons generated: {len(amend_reasons)} reasons")

            # Small delay to avoid rate limits
            await asyncio.sleep(1)

            # Step 5: Generate message templates
            logger.info("Step 5: Generating message templates...")
            message_templates = await self._generate_message_templates(bill_text, bill_metadata)
            logger.info(f"Message templates generated: {list(message_templates.keys())}")

            # Small delay to avoid rate limits
            await asyncio.sleep(1)

            # Step 6: Generate tags
            logger.info("Step 6: Generating AI tags...")
            tags = await self._generate_tags(bill_text, bill_metadata)
            logger.info(f"Tags generated: {len(tags)} tags")

            # Small delay to avoid rate limits
            await asyncio.sleep(1)

            # Step 7: Generate TL;DR
            logger.info("Step 7: Generating TL;DR...")
            tldr = await self._generate_tldr(bill_text, bill_metadata)
            logger.info(f"TL;DR generated: {len(tldr)} characters")

            # Extract simple AI summary from structured summary
            ai_summary = ""
            if structured_summary and structured_summary.get('what_does', {}).get('content'):
                ai_summary = structured_summary['what_does']['content']
            elif tldr:
                ai_summary = tldr
            else:
                ai_summary = f"This bill, {bill_metadata.get('title', 'Unknown Bill')}, is currently being processed."

            result = {
                'structured_summary': structured_summary,
                'ai_summary': ai_summary,
                'support_reasons': support_reasons,
                'oppose_reasons': oppose_reasons,
                'amend_reasons': amend_reasons,
                'message_templates': message_templates,
                'tags': tags,
                'tldr': tldr
            }

            logger.info("Complete AI analysis finished successfully")
            return result

        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return self._get_fallback_ai_data(bill_metadata)

    async def generate_detailed_bill_analysis(self, bill_text: str, bill_metadata: dict) -> dict:
        """
        Produce structured sections with claims, justifications, and citations (quotes) intended
        for BillDetails. Returns a payload that maps to BillDetails schemas.
        Robust to long texts and partial failures.
        """
        # If AI is disabled (no API key), return a safe, minimal structured payload
        if not getattr(self, 'enabled', False):
            fallback = self._get_fallback_ai_data(bill_metadata)
            return {
                "hero_summary": fallback.get("tldr") or f"This bill, {bill_metadata.get('title','')}, is currently being considered.",
                "overview": {
                    "what_does": {"content": "", "citations": []},
                    "who_affects": {"content": "", "citations": []},
                    "why_matters": {"content": "", "citations": []},
                    "key_provisions": [],
                    "cost_impact": {"content": "", "citations": []},
                    "timeline": [],
                },
                "positions": {
                    "support_reasons": [
                        {"claim": r, "justification": "", "citations": []} for r in (fallback.get("support_reasons") or [])
                    ],
                    "oppose_reasons": [
                        {"claim": r, "justification": "", "citations": []} for r in (fallback.get("oppose_reasons") or [])
                    ],
                    "amend_reasons": [
                        {"claim": r, "justification": "", "citations": []} for r in (fallback.get("amend_reasons") or [])
                    ],
                },
                "message_templates": fallback.get("message_templates") or {},
                "tags": fallback.get("tags") or [],
                "other_details": [],
            }

        # Truncate long texts to avoid model context limits
        if len(bill_text) > 50000:
            bill_text = bill_text[:50000] + "\n\n... [text truncated for processing - analysis based on first 50,000 characters]"

        # Build prompts to require quotes and short justifications; tolerate partial failures
        structured_summary = {}
        support_reasons: List[str] = []
        oppose_reasons: List[str] = []
        amend_reasons: List[str] = []
        message_templates: dict = {}
        tags: List[str] = []
        tldr = ""

        try:
            structured_summary = await self._generate_summary(bill_text, bill_metadata)
        except Exception as e:
            logger.error(f"_generate_summary failed: {e}")
            structured_summary = self._get_fallback_structured_summary(bill_metadata)
        try:
            support_reasons = await self._generate_support_reasons(bill_text, bill_metadata)
        except Exception as e:
            logger.error(f"_generate_support_reasons failed: {e}")
            support_reasons = []
        try:
            oppose_reasons = await self._generate_oppose_reasons(bill_text, bill_metadata)
        except Exception as e:
            logger.error(f"_generate_oppose_reasons failed: {e}")
            oppose_reasons = []
        try:
            amend_reasons = await self._generate_amend_reasons(bill_text, bill_metadata)
        except Exception as e:
            logger.error(f"_generate_amend_reasons failed: {e}")
            amend_reasons = []
        try:
            message_templates = await self._generate_message_templates(bill_text, bill_metadata)
        except Exception as e:
            logger.error(f"_generate_message_templates failed: {e}")
            message_templates = {}
        try:
            tags = await self._generate_tags(bill_text, bill_metadata)
        except Exception as e:
            logger.error(f"_generate_tags failed: {e}")
            tags = []
        try:
            tldr = await self._generate_tldr(bill_text, bill_metadata)
        except Exception as e:
            logger.error(f"_generate_tldr failed: {e}")
            tldr = f"This bill, {bill_metadata.get('title','')}, is currently being considered."

        # Initial shape; citation enrichment will be performed by BillDetailsService
        # Normalize arrays that may be returned under different keys by the model
        kp_src = structured_summary.get("key_provisions")
        kp_items = []
        if isinstance(kp_src, dict):
            kp_items = (kp_src.get("items") or kp_src.get("provisions") or [])
        elif isinstance(kp_src, list):
            kp_items = kp_src

        tl_src = structured_summary.get("timeline")
        tl_items = []
        if isinstance(tl_src, dict):
            tl_items = (tl_src.get("items") or tl_src.get("milestones") or [])
        elif isinstance(tl_src, list):
            tl_items = tl_src

        overview = {
            "what_does": {"content": (structured_summary.get("what_does", {}) or {}).get("content", ""), "citations": []},
            "who_affects": {"content": (structured_summary.get("who_affects", {}) or {}).get("content", ""), "citations": []},
            "why_matters": {"content": (structured_summary.get("why_matters", {}) or {}).get("content", ""), "citations": []},
            "key_provisions": [
                {"content": item, "citations": []}
                for item in (kp_items or [])
            ],
            "cost_impact": {"content": (structured_summary.get("cost_impact", {}) or {}).get("content", ""), "citations": []},
            "timeline": [
                {"content": item, "citations": []}
                for item in (tl_items or [])
            ],
        }

        # If everything is empty, seed 'what_does' with TLDR so UI has content
        if not any([
            overview["what_does"]["content"], overview["who_affects"]["content"], overview["why_matters"]["content"],
            overview["key_provisions"], overview["cost_impact"]["content"], overview["timeline"]
        ]):
            overview["what_does"]["content"] = tldr or f"This bill, {bill_metadata.get('title','')}, is currently being considered."

        # Convert structured reasons to simple format for compatibility
        def extract_reasons(reasons_list):
            if not reasons_list:
                return []
            result = []
            for r in reasons_list:
                if isinstance(r, dict):
                    claim = r.get("claim", str(r))
                    justification = r.get("justification", "")
                    citations = r.get("citations", [])
                    result.append({"claim": claim, "justification": justification, "citations": citations})
                else:
                    result.append({"claim": str(r), "justification": "", "citations": []})
            return result

        return {
            "hero_summary": tldr,
            "overview": overview,
            "positions": {
                "support_reasons": extract_reasons(support_reasons),
                "oppose_reasons": extract_reasons(oppose_reasons),
                "amend_reasons": extract_reasons(amend_reasons),
            },
            "message_templates": message_templates or {},
            "tags": tags or [],
            "other_details": [],
        }

        # Original parallel processing (commented out to avoid rate limits)
        # tasks = [
        #     self._generate_summary(bill_text, bill_metadata),
        #     self._generate_support_reasons(bill_text, bill_metadata),
        #     self._generate_oppose_reasons(bill_text, bill_metadata),
        #     self._generate_amend_reasons(bill_text, bill_metadata),
        #     self._generate_message_templates(bill_text, bill_metadata),
        #     self._generate_tags(bill_text, bill_metadata)
        # ]

        # Original parallel processing (commented out to avoid rate limits)
        # try:
        #     results = await asyncio.gather(*tasks, return_exceptions=True)
        #
        #     # Check if any tasks failed
        #     failed_tasks = [i for i, result in enumerate(results) if isinstance(result, Exception)]
        #     if failed_tasks:
        #         logger.error(f"AI tasks failed: {[results[i] for i in failed_tasks]}")
        #         # For now, return fallback data if any task fails
        #         return self._get_fallback_ai_data(bill_metadata)
        #
        #     return {
        #         'ai_summary': results[0],
        #         'support_reasons': results[1],
        #         'oppose_reasons': results[2],
        #         'amend_reasons': results[3],
        #         'message_templates': results[4],
        #         'tags': results[5]
        #     }
        # except Exception as e:
        #     logger.error(f"AI processing failed: {e}")
        #     import traceback
        #     logger.error(f"Traceback: {traceback.format_exc()}")
        #     # Return minimal fallback data
        #     return self._get_fallback_ai_data(bill_metadata)

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_summary(self, bill_text: str, metadata: dict) -> dict:
        """Generate structured summary with exact quotes for citations"""

        # WORLD-CLASS QUALITY: Every section must have exact quotes for citations
        prompt = f"""
You are a world-class legislative analyst. Analyze this bill and provide a structured summary with EXACT QUOTES for citations.

BILL TITLE: {metadata.get('title', 'Unknown Bill')}
BILL TEXT: {bill_text}

CRITICAL REQUIREMENTS FOR WORLD-CLASS QUALITY:
1. Use 8th grade reading level - clear, simple language
2. Be factual and specific - no vague generalizations
3. Include 1-3 EXACT QUOTES from the bill text for each section
4. Quotes must be word-for-word from the bill (minimum 4 words, maximum 15 words)
5. Focus on the most important, verifiable facts

Required JSON structure:
{{
  "what_does": {{
    "content": "Clear explanation in 2-3 sentences of what this bill actually does",
    "citations": [
      {{"quote": "exact text from bill"}},
      {{"quote": "another exact quote"}}
    ]
  }},
  "who_affects": {{
    "content": "Who is specifically impacted in 1-2 sentences",
    "citations": [
      {{"quote": "exact text mentioning affected groups"}}
    ]
  }},
  "why_matters": {{
    "content": "Why this matters to regular people in 1-2 sentences",
    "citations": [
      {{"quote": "exact text showing impact or importance"}}
    ]
  }},
  "key_provisions": [
    {{
      "content": "First major provision in simple terms",
      "citations": [{{"quote": "exact text of this provision"}}]
    }},
    {{
      "content": "Second major provision in simple terms",
      "citations": [{{"quote": "exact text of this provision"}}]
    }}
  ],
  "cost_impact": {{
    "content": "Financial impact in plain English (if mentioned)",
    "citations": [
      {{"quote": "exact text about costs or funding"}}
    ]
  }},
  "timeline": [
    {{
      "content": "When this takes effect",
      "citations": [{{"quote": "exact text about timing"}}]
    }}
  ]
}}

REMEMBER: Every quote must be EXACTLY as written in the bill text. No paraphrasing.

JSON Response:
"""

        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=self.max_tokens,
            temperature=0.3
        )

        content = response.choices[0].message.content.strip()

        try:
            # Extract and parse JSON
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            json_str = content[start_idx:end_idx]

            import json
            structured_summary = json.loads(json_str)

            # Validate required sections
            required_sections = ['what_does', 'who_affects', 'why_matters', 'key_provisions']
            if all(section in structured_summary for section in required_sections):
                return structured_summary
            else:
                raise ValueError("Missing required summary sections")

        except Exception as e:
            logger.error(f"Structured summary generation failed: {e}")
            return self._get_fallback_structured_summary(metadata)

    def _get_fallback_structured_summary(self, metadata: dict) -> dict:
        """Fallback structured summary if AI fails (Implementation-2.MD approach)"""
        return {
            "what_does": {
                "title": "What This Bill Does",
                "content": f"Analysis of {metadata.get('title', 'this bill')} is being processed.",
                "key_points": ["Detailed analysis will be available soon"]
            },
            "who_affects": {
                "title": "Who This Affects",
                "content": "Impact analysis is being processed.",
                "affected_groups": ["Analysis pending"]
            },
            "why_matters": {
                "title": "Why It Matters to You",
                "content": "Personal relevance analysis is being processed.",
                "benefits": ["Analysis pending"],
                "concerns": ["Analysis pending"]
            },
            "key_provisions": {
                "title": "Key Provisions",
                "content": "Provision analysis is being processed.",
                "provisions": ["Analysis pending"]
            },
            "timeline": {
                "title": "Implementation Timeline",
                "content": "Timeline analysis not available",
                "milestones": []
            },
            "cost_impact": {
                "title": "Cost Impact",
                "content": "Cost analysis not available",
                "estimates": []
            }
        }

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_support_reasons(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate support reasons with exact quotes for citations"""
        prompt = f"""
        Generate specific reasons why someone would SUPPORT this bill, with EXACT QUOTES for citations.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        WORLD-CLASS QUALITY REQUIREMENTS:
        - 6-8 distinct, factual reasons for support
        - Each reason: clear claim + short justification + 1-2 exact quotes
        - Quotes must be word-for-word from bill text (4-15 words)
        - Focus on real benefits and positive outcomes
        - Use simple, clear language

        Format as JSON array:
        [
          {{
            "claim": "Clear reason for support (10-15 words)",
            "justification": "Brief explanation why this matters (1-2 sentences)",
            "citations": [
              {{"quote": "exact text from bill supporting this claim"}}
            ]
          }}
        ]

        Support Reasons:
        """

        # 🚫 DISABLED: Expensive gpt-4-turbo call blocked
        logger.warning("🚫 EXPENSIVE _generate_support_reasons DISABLED - use balanced analysis")
        return [
            "This bill addresses an important issue",
            "The provisions make sense for our community",
            "This legislation is needed to solve current problems"
        ]

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_oppose_reasons(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate opposition reasons with exact quotes for citations"""
        prompt = f"""
        Generate specific reasons why someone would OPPOSE this bill, with EXACT QUOTES for citations.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        WORLD-CLASS QUALITY REQUIREMENTS:
        - 6-8 distinct, factual reasons for opposition
        - Each reason: clear concern + short justification + 1-2 exact quotes
        - Quotes must be word-for-word from bill text (4-15 words)
        - Focus on real concerns and potential negative impacts
        - Use simple, clear language

        Format as JSON array:
        [
          {{
            "claim": "Clear reason for opposition (10-15 words)",
            "justification": "Brief explanation of the concern (1-2 sentences)",
            "citations": [
              {{"quote": "exact text from bill supporting this concern"}}
            ]
          }}
        ]

        Opposition Reasons:
        """

        # 🚫 DISABLED: Expensive AI call blocked
        logger.warning("🚫 EXPENSIVE _generate_oppose_reasons DISABLED - use balanced analysis")
        return [
            "This bill may have unintended consequences",
            "The legislation could be too costly to implement",
            "There are concerns about federal overreach"
        ]

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_amend_reasons(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate amendment reasons with exact quotes for citations"""
        prompt = f"""
        Generate specific reasons why someone would want to AMEND this bill, with EXACT QUOTES for citations.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        WORLD-CLASS QUALITY REQUIREMENTS:
        - 6-8 distinct, factual reasons for amendments
        - Each reason: clear improvement needed + short justification + 1-2 exact quotes
        - Quotes must be word-for-word from bill text (4-15 words)
        - Focus on realistic improvements and modifications
        - Use simple, clear language

        Format as JSON array:
        [
          {{
            "claim": "Clear amendment needed (10-15 words)",
            "justification": "Brief explanation why this change is needed (1-2 sentences)",
            "citations": [
              {{"quote": "exact text from bill that needs improvement"}}
            ]
          }}
        ]

        Amendment Reasons:
        """

        # 🚫 DISABLED: Expensive gpt-4-turbo call blocked
        logger.warning("🚫 EXPENSIVE _generate_amend_reasons DISABLED - use balanced analysis")
        return [
            "The bill needs stronger enforcement mechanisms",
            "Some provisions should be clarified or refined",
            "Additional protections should be included"
        ]

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_message_templates(self, bill_text: str, metadata: dict) -> dict:
        """Generate message templates for each position"""
        prompt = f"""
        Create professional message templates for contacting representatives about this bill.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Generate three message templates:
        1. SUPPORT template - professional message supporting the bill
        2. OPPOSE template - professional message opposing the bill
        3. AMEND template - professional message requesting amendments

        Each template should:
        - Be 2-3 sentences
        - Sound professional but personal
        - Include specific bill details
        - Have placeholders for [REPRESENTATIVE_NAME] and [CONSTITUENT_NAME]

        Format as JSON:
        {{
          "support": "Template text...",
          "oppose": "Template text...",
          "amend": "Template text..."
        }}

        Templates:
        """

        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=min(self.max_tokens, 600),
            temperature=0.3
        )

        try:
            content = response.choices[0].message.content.strip()
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            json_str = content[start_idx:end_idx]
            templates = json.loads(json_str)

            if isinstance(templates, dict) and all(k in templates for k in ['support', 'oppose', 'amend']):
                return templates
            else:
                raise ValueError("Invalid format")

        except Exception as e:
            logger.warning(f"Failed to parse message templates JSON: {e}")
            return {
                "support": "Dear [REPRESENTATIVE_NAME], I am writing to express my support for this important legislation. As your constituent, I believe this bill will benefit our community and urge you to vote in favor. Thank you for your consideration.",
                "oppose": "Dear [REPRESENTATIVE_NAME], I am writing to express my concerns about this proposed legislation. As your constituent, I believe this bill may have negative impacts and urge you to vote against it. Thank you for your consideration.",
                "amend": "Dear [REPRESENTATIVE_NAME], I am writing about this proposed legislation. While I see merit in its goals, I believe amendments are needed to address certain concerns. I urge you to work toward improving this bill. Thank you for your consideration."
            }

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_tags(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate categorization tags for the bill"""
        prompt = f"""
        Generate categorization tags for this bill to help with organization and search.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Create 5-8 tags that categorize this bill. Tags should be:
        - Single words or short phrases (1-3 words)
        - Relevant to the bill's subject matter
        - Useful for search and filtering
        - Common policy areas (healthcare, education, environment, etc.)

        Format as a JSON array of strings:
        ["tag1", "tag2", "tag3", ...]

        Tags:
        """

        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=min(self.max_tokens, 200),
            temperature=0.3
        )

        try:
            content = response.choices[0].message.content.strip()
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            json_str = content[start_idx:end_idx]
            tags = json.loads(json_str)

            if isinstance(tags, list) and all(isinstance(t, str) for t in tags):
                return tags[:8]
            else:
                raise ValueError("Invalid format")

        except Exception as e:
            logger.warning(f"Failed to parse tags JSON: {e}")
            return ["legislation", "policy", "government"]

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_tldr(self, bill_text: str, metadata: dict) -> str:
        """Generate TL;DR - Ultra-simple explanation in plain English for 8th grade reading level"""

        prompt = f"""
You are an expert at making legislation accessible and engaging for everyday citizens.

BILL TITLE: {metadata.get('title', 'Unknown Bill')}
BILL TEXT: {bill_text}

Create an engaging TL;DR summary that:
- Starts with "TL;DR" and includes a relevant emoji (🌍, 💰, 🏥, ⚖️, 🏠, 🚗, 📚, 🔒, etc.)
- Is 2-3 sentences that capture the bill's impact and significance
- Uses compelling, accessible language that makes people care
- Highlights key numbers, benefits, or changes when available
- Focuses on WHY this matters to ordinary people
- Avoids dry bureaucratic language - make it engaging!

Examples of engaging TL;DR style:
- "TL;DR 🌍 This critical climate bill would transform America's energy future by investing $500B in clean energy infrastructure, creating 2M green jobs, and putting the US on track to meet Paris Agreement goals."
- "TL;DR 🏥 This healthcare bill would expand Medicare to cover dental and vision care for 65M seniors, potentially saving families thousands per year in out-of-pocket costs."
- "TL;DR 📚 This education bill would provide free community college to all Americans and forgive up to $10K in student debt, helping millions access higher education without crushing debt."

Write an engaging TL;DR that makes this legislation compelling and relevant:
"""

        response = await self.client.chat.completions.create(
            model="gpt-4-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,  # Allow longer engaging summaries
            temperature=0.3  # Slightly more creative for engaging content
        )

        tldr = response.choices[0].message.content.strip()

        # Clean up the response - remove any extra formatting but keep emojis
        tldr = tldr.replace("**", "").strip()  # Remove bold formatting

        # Ensure it starts with "TL;DR" (add if missing)
        if not tldr.startswith("TL;DR"):
            tldr = "TL;DR " + tldr

        # Ensure reasonable length (safety check - allow longer engaging content)
        if len(tldr.split()) > 80:
            # If too long, take first two sentences
            sentences = tldr.split('.')
            if len(sentences) >= 2:
                tldr = sentences[0] + '.' + sentences[1] + '.'
            else:
                tldr = tldr[:400] + '...'

        return tldr

    async def _make_openai_request(self, messages: list, max_tokens: int = 800, temperature: float = 0.7) -> str:
        """Generic method for making OpenAI API requests"""
        if not self.enabled:
            raise RuntimeError("AI service is not enabled - OpenAI API key not configured")

        response = await self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            max_tokens=min(self.max_tokens, max_tokens),
            temperature=temperature
        )

        return response.choices[0].message.content.strip()

    def _get_fallback_ai_data(self, metadata: dict) -> dict:
        """Return fallback data when AI processing fails"""
        return {
            'ai_summary': f"This bill, {metadata['title']}, is currently being processed. Full AI analysis will be available soon.",
            'support_reasons': [
                "This bill addresses an important issue",
                "The legislation could benefit our community",
                "This represents good policy direction"
            ],
            'oppose_reasons': [
                "This bill may have unintended consequences",
                "The legislation could be too costly",
                "There are concerns about implementation"
            ],
            'amend_reasons': [
                "The bill needs stronger enforcement",
                "Some provisions should be clarified",
                "Additional protections are needed"
            ],
            'message_templates': {
                "support": "Dear [REPRESENTATIVE_NAME], I am writing to express my support for this important legislation. As your constituent, I believe this bill will benefit our community. Thank you for your consideration.",
                "oppose": "Dear [REPRESENTATIVE_NAME], I am writing to express my concerns about this proposed legislation. As your constituent, I have reservations about its potential impacts. Thank you for your consideration.",
                "amend": "Dear [REPRESENTATIVE_NAME], I am writing about this proposed legislation. While I see merit in its goals, I believe amendments would improve it. Thank you for your consideration."
            },
            'tags': ["legislation", "policy", "government"],
            'tldr': f"This bill is about {metadata.get('title', 'legislation')} and is currently being analyzed."
        }


    # ========== SPAN-GROUNDED ANALYSIS METHODS ==========

    async def _extract_evidence_spans(self, bill_text: str, bill_metadata: Dict) -> Dict[str, List[Dict]]:
        """Extract ranked evidence spans using enhanced retriever."""

        # Use enhanced span retriever for better quality spans
        enhanced_result = self.span_retriever.extract_enhanced_spans(bill_text, bill_metadata)

        # Convert to expected format
        evidence_spans = enhanced_result.get('evidence', [])

        # Convert enhanced spans to expected format
        spans = {
            'tldr': [],
            'who': [],
            'budget': []
        }

        # Distribute spans by type and priority
        for span in evidence_spans:
            span_type = span.get('type', 'general')
            priority = span.get('priority', 'low')

            # Convert to expected format
            formatted_span = {
                'a': span.get('anchor_id', f"span-{len(spans['tldr']) + len(spans['who']) + len(spans['budget'])}"),
                'h': span.get('heading', 'Unknown Section'),
                'st': span.get('start_offset', 0),
                'en': span.get('end_offset', 0),
                'q': span.get('quote', '')[:100],  # Limit quote length
                'priority': priority,
                'span_type': span_type
            }

            # Route spans to categories based on type and content
            if span_type == 'budget' or span.get('budget_category'):
                spans['budget'].append(formatted_span)
                if priority == 'high':
                    spans['tldr'].append(formatted_span)  # High priority budget items go to TLDR

            elif span_type == 'mandate':
                spans['who'].append(formatted_span)
                if priority == 'high':
                    spans['tldr'].append(formatted_span)  # High priority mandates go to TLDR

            elif span_type == 'scope':
                spans['who'].append(formatted_span)  # Scope affects who

            else:  # general spans
                spans['tldr'].append(formatted_span)

        # Limit spans per category and prioritize by priority
        for category in spans:
            # Sort by priority (high > medium > low) then by quote length
            priority_order = {'high': 3, 'medium': 2, 'low': 1}
            spans[category] = sorted(
                spans[category],
                key=lambda x: (priority_order.get(x.get('priority', 'low'), 1), len(x.get('q', ''))),
                reverse=True
            )
            spans[category] = spans[category][:3]  # Max 3 spans per category

        logger.info(f"📊 Enhanced spans: tldr={len(spans['tldr'])}, who={len(spans['who'])}, budget={len(spans['budget'])}")
        logger.info(f"📊 Routing: {enhanced_result.get('routing', 'unknown')}, total_spans={enhanced_result.get('total_spans', 0)}")

        return spans

    def _validate_span_coverage(self, spans: Dict[str, List[Dict]]) -> bool:
        """Validate that we have sufficient evidence spans for analysis."""
        # Require at least 1 span for TLDR, and at least 2 total spans
        tldr_spans = len(spans.get('tldr', []))
        total_spans = sum(len(spans.get(cat, [])) for cat in spans)

        coverage_ok = tldr_spans >= 1 and total_spans >= 2
        logger.info(f"📋 Span coverage: tldr={tldr_spans}, total={total_spans}, sufficient={coverage_ok}")
        return coverage_ok

    async def _handle_insufficient_evidence(self, bill_title: str, bill_text: str) -> Dict[str, Any]:
        """Handle cases where insufficient evidence spans are found - do AI analysis anyway."""
        logger.warning("⚠️ Insufficient evidence spans - proceeding with direct text analysis")

        # Instead of returning placeholder content, do actual AI analysis on the bill text
        # This ensures we get real analysis even for bills with poor span extraction
        
        if not bill_text or len(bill_text.strip()) < 100:
            # Only return placeholder if we truly have no content
            logger.warning("No bill text available - using placeholder")
            return await self._handle_placeholder_bill(bill_title, bill_text)
        
        try:
            # Create a minimal but real analysis using direct text processing
            bill_metadata = {"title": bill_title}
            
            # Use the simple approach but with actual AI calls
            prompt = f"""
Analyze this bill and provide a comprehensive summary.

Bill Title: {bill_title}

Bill Text:
{bill_text[:10000]}  # Use first 10k chars to stay within token limits

Please provide:
1. A clear TLDR summary (2-3 sentences)
2. Who is affected by this legislation
3. Why this matters to the public
4. Budget/financial impact if any
5. 3-5 key points about what the bill does
6. 2-3 reasons someone might support this
7. 2-3 reasons someone might oppose this
8. 2-3 suggestions for amendments

Respond in JSON format with these exact fields:
{{
  "tldr": "...",
  "who_affected": "...", 
  "why_matters": "...",
  "budget_impact": "...",
  "key_points": ["...", "...", "..."],
  "support_reasons": ["...", "..."],
  "oppose_reasons": ["...", "..."],
  "amend_suggestions": ["...", "..."]
}}
"""

            # Make actual AI call with reasonable budget
            import openai
            
            response = await openai.ChatCompletion.acreate(
                model="gpt-4o-mini",  # Cost-effective but still real AI
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=800,
                timeout=30
            )
            
            import json
            analysis = json.loads(response.choices[0].message.content)
            
            # Calculate rough cost (gpt-4o-mini is ~$0.15 per 1M tokens)
            prompt_tokens = len(prompt.split()) * 1.3
            completion_tokens = len(response.choices[0].message.content.split()) * 1.3
            cost = (prompt_tokens * 0.00015 + completion_tokens * 0.0006) / 1000
            
            logger.info(f"✅ Insufficient evidence handled with real AI: ${cost:.4f}")
            
            return {
                "success": True,
                "processing_level": "direct_analysis_insufficient_evidence",
                "summary": analysis,
                "extraction": {
                    "key_points": analysis.get("key_points", []),
                    "complete_analysis": [{
                        "title": "Bill Analysis",
                        "description": analysis.get("tldr", ""),
                        "importance": "primary"
                    }]
                },
                "cost_optimized": True,
                "_metadata": {
                    "model": "gpt-4o-mini",
                    "cost": cost,
                    "tokens": int(prompt_tokens + completion_tokens),
                    "evidence_coverage": False,
                    "direct_analysis": True
                }
            }
            
        except Exception as e:
            logger.error(f"Direct analysis failed: {e}")
            # Fall back to placeholder only if AI call fails
            return await self._handle_placeholder_bill(bill_title, bill_text)

    async def _analyze_with_spans(self, spans: Dict[str, List[Dict]], bill_metadata: Dict) -> Dict[str, Any]:
        """Perform strict span-grounded analysis using your JSON schema."""

        # Create the strict span-grounded prompt
        system_message = """Use only the provided quotes as evidence. Every sentence must have at least one evidence item whose q appears verbatim within the sentence or is a precise paraphrase. If evidence is insufficient, return the string INSUFFICIENT_EVIDENCE instead of making something up. Output only the JSON for the given schema."""

        user_content = {
            "bill": {"id": bill_metadata.get('bill_id', 'unknown')},
            "spans": spans
        }

        # JSON Schema with short keys and required evidence
        json_schema = {
            "type": "object",
            "properties": {
                "tldr": {
                    "type": "object",
                    "properties": {
                        "s": {"type": "string", "maxLength": 220},
                        "ev": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "a": {"type": "string"},
                                    "st": {"type": "integer"},
                                    "en": {"type": "integer"},
                                    "q": {"type": "string"}
                                },
                                "required": ["a", "st", "en", "q"]
                            }
                        }
                    },
                    "required": ["s", "ev"]
                },
                "who": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "s": {"type": "string", "maxLength": 180},
                            "ev": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "a": {"type": "string"},
                                        "st": {"type": "integer"},
                                        "en": {"type": "integer"},
                                        "q": {"type": "string"}
                                    },
                                    "required": ["a", "st", "en", "q"]
                                }
                            }
                        },
                        "required": ["s", "ev"]
                    },
                    "maxItems": 3
                },
                "budget": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "s": {"type": "string", "maxLength": 160},
                            "ev": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "a": {"type": "string"},
                                        "st": {"type": "integer"},
                                        "en": {"type": "integer"},
                                        "q": {"type": "string"}
                                    },
                                    "required": ["a", "st", "en", "q"]
                                }
                            }
                        },
                        "required": ["s", "ev"]
                    },
                    "maxItems": 3
                }
            },
            "required": ["tldr", "who", "budget"],
            "additionalProperties": False
        }

        try:
            start_time = time.time()
            response = await self.client.chat.completions.create(
                model="gpt-4o",  # Use quality model for span analysis
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": json.dumps(user_content)}
                ],
                max_tokens=350,  # Optimized for span-grounded output
                temperature=0.1,  # Very low for precision
                response_format={"type": "json_object", "schema": json_schema}
            )

            response_time_ms = (time.time() - start_time) * 1000

            # Track AI usage
            track_openai_response(
                response=response,
                operation_type="span_grounded_analysis",
                operation_subtype="evidence_based_analysis",
                model_name="gpt-4o",
                prompt_length=len(json.dumps(user_content)),
                response_time_ms=response_time_ms,
                success=True
            )

            # Parse and validate response
            result = json.loads(response.choices[0].message.content)

            # Convert to standard format for validation
            standard_format = self._convert_span_result_for_validation(result)

            # Run comprehensive span-grounded validation
            is_valid, validation_errors, validation_metrics = self.validator.validate_analysis(standard_format)

            if not is_valid:
                logger.warning(f"Span-grounded validation failed: {validation_errors}")
                logger.warning(f"Validation metrics: {validation_metrics}")
                # FAIL-CLOSED: Return error instead of publishing bad content
                return {
                    "success": False,
                    "error": "Analysis failed quality validation",
                    "validation_errors": validation_errors,
                    "validation_metrics": validation_metrics,
                    "needs_human_review": True
                }

            # Validate evidence coverage (legacy check)
            if not self._validate_evidence_coverage(result):
                logger.warning("Evidence coverage validation failed")
                return await self._handle_insufficient_evidence(
                    bill_metadata.get('title', 'Unknown Bill'),
                    ""  # We don't have bill_text here
                )

            # Convert to standard format
            return self._convert_spans_to_standard_format(result, response, response_time_ms)

        except Exception as e:
            logger.error(f"Span-grounded analysis failed: {e}")
            return await self._handle_insufficient_evidence(
                bill_metadata.get('title', 'Unknown Bill'),
                ""
            )

    def _convert_span_result_for_validation(self, span_result: Dict) -> Dict[str, Any]:
        """Convert span result to format expected by validator"""

        def convert_evidence(ev_list):
            """Convert evidence from short format to validation format"""
            return [
                {
                    "quote": item.get("q", ""),
                    "heading": item.get("h", item.get("a", "")),  # Use anchor as heading fallback
                    "anchor_id": item.get("a", ""),
                    "start_offset": item.get("st", 0),
                    "end_offset": item.get("en", 0)
                }
                for item in ev_list
            ]

        # Convert to validation format
        validation_format = {}

        # TLDR
        tldr = span_result.get("tldr", {})
        if tldr:
            validation_format["tldr"] = {
                "content": tldr.get("s", ""),
                "citations": convert_evidence(tldr.get("ev", []))
            }

        # Who affects
        who_items = span_result.get("who", [])
        validation_format["who_affects"] = [
            {
                "content": item.get("s", ""),
                "citations": convert_evidence(item.get("ev", []))
            }
            for item in who_items
        ]

        # Budget/key provisions
        budget_items = span_result.get("budget", [])
        validation_format["key_provisions"] = [
            {
                "content": item.get("s", ""),
                "citations": convert_evidence(item.get("ev", []))
            }
            for item in budget_items
        ]

        # Why matters (synthesized from available content)
        validation_format["why_matters"] = {
            "content": "Legislative analysis based on evidence spans",
            "citations": convert_evidence(tldr.get("ev", [])[:1])  # Use first TLDR citation
        }

        return validation_format

    def _convert_enriched_for_validation(self, enriched_analysis: Dict) -> Dict[str, Any]:
        """Convert enriched analysis format for validation"""

        complete_analysis = enriched_analysis.get('complete_analysis', [])
        if not complete_analysis:
            return {}

        # Use first section for validation (simplified)
        first_section = complete_analysis[0]

        # Convert citations to validation format
        def convert_citations(citations_list):
            converted = []
            for citation in citations_list:
                if isinstance(citation, dict):
                    converted.append({
                        "quote": citation.get('q', ''),
                        "heading": citation.get('h', ''),
                        "anchor_id": citation.get('a', ''),
                        "start_offset": citation.get('st', 0),
                        "end_offset": citation.get('en', 0)
                    })
            return converted

        # Get citations from first section
        section_citations = convert_citations(first_section.get('citations', []))

        # Create validation format with proper citations
        validation_format = {
            "tldr": {
                "content": first_section.get('detailed_summary', '') or 'Analysis completed',
                "citations": section_citations[:1] if section_citations else []
            },
            "who_affects": [
                {
                    "content": party,
                    "citations": section_citations[:1] if section_citations else []
                }
                for party in first_section.get('affected_parties', [])[:2]
            ],
            "why_matters": {
                "content": "Legislative analysis with evidence-based details",
                "citations": section_citations[:1] if section_citations else []
            },
            "key_provisions": [
                {
                    "content": action,
                    "citations": section_citations[:1] if section_citations else []
                }
                for action in first_section.get('key_actions', [])[:2]
            ]
        }

        return validation_format

    def _convert_enriched_to_standard_format(self, enriched_result: Dict) -> Dict[str, Any]:
        """Convert enriched analysis to standard format for compatibility"""

        analysis = enriched_result['analysis']
        cost_breakdown = enriched_result['cost_breakdown']

        complete_analysis = analysis.get('complete_analysis', [])
        additional_details = analysis.get('additional_details', {})

        # Extract summary information from first section
        primary_section = None
        for section in complete_analysis:
            if section.get('importance') == 'primary':
                primary_section = section
                break

        if not primary_section and complete_analysis:
            primary_section = complete_analysis[0]

        # Build standard format
        standard_format = {
            "success": True,
            "processing_level": "enriched_comprehensive",
            "summary": {
                "tldr": primary_section.get('detailed_summary', 'Comprehensive analysis completed') if primary_section else 'Analysis completed',
                "who_affected": '; '.join(primary_section.get('affected_parties', [])) if primary_section else 'Multiple parties affected',
                "why_matters": f"This legislation includes {len(complete_analysis)} major provisions with comprehensive compliance requirements",
                "budget_impact": self._extract_budget_summary(additional_details),
                "key_points": [section.get('title', '') for section in complete_analysis[:5]],
                "support_reasons": ["Comprehensive analysis supports legislative clarity"],
                "oppose_reasons": ["Detailed analysis identifies implementation challenges"],
                "amend_suggestions": ["Evidence-based analysis suggests targeted improvements"]
            },
            "extraction": {
                "key_points": [action for section in complete_analysis for action in section.get('key_actions', [])],
                "complete_analysis": complete_analysis,
                "additional_details": additional_details
            },
            "cost_optimized": True,
            "_metadata": {
                "model": "gpt-4o-mini + gpt-4o",
                "cost": cost_breakdown['total_cost'],
                "cost_breakdown": cost_breakdown,
                "sections_analyzed": len(complete_analysis),
                "sections_enriched": cost_breakdown['sections_enriched'],
                "free_enrichments": analysis.get('processing_metadata', {}).get('free_enrichments', 0),
                "quality_validated": True,
                "evidence_coverage": True,
                "enriched_analysis": True,
                "budget_exhausted": cost_breakdown.get('budget_exhausted', False)
            }
        }

        return standard_format

    def _extract_budget_summary(self, additional_details: Dict) -> str:
        """Extract budget summary from additional details"""

        budgets = additional_details.get('budgets_table', [])
        if not budgets:
            return "Budget impact analysis based on available evidence"

        budget_items = []
        for budget in budgets[:3]:  # Top 3 budget items
            kind = budget.get('kind', 'funding')
            amount = budget.get('amount', 'unspecified amount')
            budget_items.append(f"{kind}: {amount}")

        return '; '.join(budget_items)

    def _convert_world_class_for_validation(self, world_class_analysis: Dict) -> Dict[str, Any]:
        """Convert world-class analysis format for validation"""

        complete_analysis = world_class_analysis.get('complete_analysis', [])
        if not complete_analysis:
            return {}

        # Use first section for validation
        first_section = complete_analysis[0]

        validation_format = {
            "tldr": {
                "content": first_section.get('detailed_summary', first_section.get('title', '')),
                "citations": first_section.get('citations', [])
            },
            "who_affects": [
                {
                    "content": party,
                    "citations": first_section.get('citations', [])[:1]
                }
                for party in first_section.get('affected_parties', [])[:2]
            ],
            "why_matters": {
                "content": "World-class analysis with comprehensive details and evidence",
                "citations": first_section.get('citations', [])[:1]
            },
            "key_provisions": [
                {
                    "content": action,
                    "citations": first_section.get('citations', [])[:1]
                }
                for action in first_section.get('key_actions', [])[:2]
            ]
        }

        return validation_format

    def _convert_world_class_to_standard_format(self, world_class_result: Dict) -> Dict[str, Any]:
        """Convert world-class analysis to standard format for compatibility"""

        analysis = world_class_result['analysis']
        cost_breakdown = world_class_result['cost_breakdown']

        complete_analysis = analysis.get('complete_analysis', [])
        additional_details = analysis.get('additional_details', {})

        # Extract summary information from first section
        primary_section = None
        for section in complete_analysis:
            if section.get('importance') == 'primary':
                primary_section = section
                break

        if not primary_section and complete_analysis:
            primary_section = complete_analysis[0]

        # Build standard format
        standard_format = {
            "success": True,
            "processing_level": "world_class_comprehensive",
            "summary": {
                "tldr": primary_section.get('detailed_summary', 'World-class analysis completed') if primary_section else 'Analysis completed',
                "who_affected": '; '.join(primary_section.get('affected_parties', [])) if primary_section else 'Multiple parties affected',
                "why_matters": f"This legislation includes {len(complete_analysis)} major provisions with comprehensive compliance requirements and evidence-based analysis",
                "budget_impact": self._extract_budget_summary_world_class(additional_details),
                "key_points": [section.get('title', '') for section in complete_analysis[:5]],
                "support_reasons": ["World-class analysis supports legislative clarity and transparency"],
                "oppose_reasons": ["Comprehensive analysis identifies potential implementation challenges"],
                "amend_suggestions": ["Evidence-based analysis suggests targeted improvements for better outcomes"]
            },
            "extraction": {
                "key_points": [action for section in complete_analysis for action in section.get('key_actions', [])],
                "complete_analysis": complete_analysis,
                "additional_details": additional_details
            },
            "cost_optimized": True,
            "_metadata": {
                "model": "gpt-4o-mini (world-class)",
                "cost": cost_breakdown['total_cost'],
                "cost_breakdown": cost_breakdown,
                "sections_analyzed": len(complete_analysis),
                "sections_enriched": cost_breakdown['sections_enriched'],
                "free_enrichments": analysis.get('processing_metadata', {}).get('free_enrichments', 0),
                "quality_validated": True,
                "evidence_coverage": True,
                "world_class_analysis": True,
                "budget_exhausted": cost_breakdown.get('budget_exhausted', False),
                "processing_method": "world_class_two_pass_evidence_by_id"
            }
        }

        return standard_format

    def _extract_budget_summary_world_class(self, additional_details: Dict) -> str:
        """Extract budget summary from world-class additional details"""

        budgets = additional_details.get('budgets_table', [])
        if not budgets:
            return "Comprehensive budget impact analysis based on evidence spans"

        budget_items = []
        for budget in budgets[:3]:  # Top 3 budget items
            kind = budget.get('kind', 'funding')
            amount = budget.get('amount', 'unspecified amount')
            budget_items.append(f"{kind}: {amount}")

        return '; '.join(budget_items)

    def _is_placeholder_bill(self, bill_title: str, bill_text: str) -> bool:
        """Check if this is a placeholder bill with no real content"""

        # Check for placeholder indicators in title
        placeholder_title_patterns = [
            r'placeholder',
            r'to be determined',
            r'tbd',
            r'draft',
            r'template'
        ]

        title_lower = bill_title.lower()
        for pattern in placeholder_title_patterns:
            if pattern in title_lower:
                return True

        # Check for minimal content
        if len(bill_text.strip()) < 200:  # Very short bills are likely placeholders
            return True

        # Check for placeholder content patterns
        placeholder_content_patterns = [
            r'this bill is a placeholder',
            r'content to be added',
            r'draft legislation',
            r'template bill'
        ]

        text_lower = bill_text.lower()
        for pattern in placeholder_content_patterns:
            if pattern in text_lower:
                return True

        return False

    def _validate_evidence_coverage(self, result: Dict) -> bool:
        """Validate that 100% of sentences have evidence coverage."""
        try:
            # Check TLDR has evidence
            tldr = result.get('tldr', {})
            if not tldr.get('ev') or len(tldr.get('ev', [])) == 0:
                logger.warning("TLDR missing evidence")
                return False

            # Check who items have evidence
            who_items = result.get('who', [])
            for item in who_items:
                if not item.get('ev') or len(item.get('ev', [])) == 0:
                    logger.warning("Who item missing evidence")
                    return False

            # Check budget items have evidence
            budget_items = result.get('budget', [])
            for item in budget_items:
                if not item.get('ev') or len(item.get('ev', [])) == 0:
                    logger.warning("Budget item missing evidence")
                    return False

            logger.info("✅ Evidence coverage validation passed")
            return True

        except Exception as e:
            logger.error(f"Evidence coverage validation failed: {e}")
            return False

    def _convert_spans_to_standard_format(self, span_result: Dict, response, response_time_ms: float) -> Dict[str, Any]:
        """Convert span-grounded result to standard format."""

        # Calculate cost
        cost = (response.usage.prompt_tokens * 0.0025 + response.usage.completion_tokens * 0.01) / 1000

        # Extract content from span format
        tldr_content = span_result.get('tldr', {}).get('s', 'Analysis completed')

        who_content = []
        for item in span_result.get('who', []):
            who_content.append(item.get('s', ''))

        budget_content = []
        for item in span_result.get('budget', []):
            budget_content.append(item.get('s', ''))

        return {
            "success": True,
            "processing_level": "span_grounded",
            "summary": {
                "tldr": tldr_content,
                "who_affected": '; '.join(who_content) if who_content else "Analysis based on available evidence",
                "why_matters": "Analysis based on legislative evidence and citations",
                "budget_impact": '; '.join(budget_content) if budget_content else "Budget impact analysis based on available evidence",
                "key_points": [item.get('s', '') for item in span_result.get('who', []) + span_result.get('budget', [])],
                "support_reasons": ["Evidence-based analysis supports legislative intent"],
                "oppose_reasons": ["Evidence-based analysis identifies potential concerns"],
                "amend_suggestions": ["Evidence-based analysis suggests potential improvements"]
            },
            "extraction": {
                "key_points": [item.get('s', '') for item in span_result.get('who', []) + span_result.get('budget', [])]
            },
            "cost_optimized": True,
            "_metadata": {
                "model": "gpt-4o",
                "cost": cost,
                "tokens": response.usage.prompt_tokens + response.usage.completion_tokens,
                "quality_validated": True,
                "evidence_coverage": True,
                "span_grounded": True,
                "response_time_ms": response_time_ms
            },
            "_evidence": span_result  # Preserve original span data for debugging
        }


    def _convert_balanced_to_standard_format(self, balanced_result: Dict) -> Dict[str, Any]:
        """Convert balanced analysis to standard format for compatibility"""

        analysis = balanced_result['analysis']
        cost_breakdown = balanced_result['cost_breakdown']
        user_content = analysis.get('user_content', {})

        complete_analysis = analysis.get('complete_analysis', [])
        additional_details = analysis.get('additional_details', {})

        # Extract summary information from analysis
        primary_section = None
        for section in complete_analysis:
            if section.get('importance') == 'primary':
                primary_section = section
                break

        if not primary_section and complete_analysis:
            primary_section = complete_analysis[0]

        # Build standard format with actual analysis content (no generic fallbacks)
        standard_format = {
            "success": True,
            "processing_level": "balanced_premium_efficient",
            "summary": {
                "tldr": self._extract_quality_tldr(primary_section, complete_analysis),
                "who_affected": self._extract_affected_parties(primary_section, additional_details),
                "why_matters": self._extract_why_matters(primary_section, complete_analysis),
                "budget_impact": self._extract_budget_impact(primary_section, additional_details),
                "key_points": [section.get('title', '') for section in complete_analysis[:5] if section.get('title')],
                "support_reasons": [reason.get('reason', '') for reason in user_content.get('support_reasons', []) if reason.get('reason')],
                "oppose_reasons": [reason.get('reason', '') for reason in user_content.get('oppose_reasons', []) if reason.get('reason')],
                "amend_suggestions": [suggestion.get('suggestion', '') for suggestion in user_content.get('amend_reasons', []) if suggestion.get('suggestion')]
            },
            "extraction": {
                "key_points": [action for section in complete_analysis for action in section.get('key_actions', [])],
                "complete_analysis": complete_analysis,
                "additional_details": additional_details,
                "user_content": user_content
            },
            "cost_optimized": True,
            "_metadata": {
                "model": "balanced (gpt-4o + gpt-4o-mini)",
                "cost": cost_breakdown['total_cost'],
                "cost_breakdown": cost_breakdown,
                "sections_analyzed": len(complete_analysis),
                "premium_content_cost": cost_breakdown.get('premium_content_cost', 0),
                "background_processing_cost": cost_breakdown['total_cost'] - cost_breakdown.get('premium_content_cost', 0),
                "quality_validated": True,
                "evidence_coverage": True,
                "balanced_analysis": True,
                "budget_exhausted": cost_breakdown.get('budget_exhausted', False),
                "processing_method": "balanced_premium_efficient"
            }
        }

        # CRITICAL: Preserve the details_payload from balanced analysis
        if 'details_payload' in balanced_result:
            standard_format['details_payload'] = balanced_result['details_payload']

        return standard_format
    
    def _extract_quality_tldr(self, primary_section: Dict, complete_analysis: List[Dict]) -> str:
        """Extract meaningful TLDR from analysis content, no generic fallbacks"""
        if primary_section and primary_section.get('detailed_summary'):
            return primary_section['detailed_summary']
        
        if primary_section and primary_section.get('description'):
            return primary_section['description']
        
        # Try to build from key actions
        if complete_analysis:
            key_actions = []
            for section in complete_analysis[:2]:  # Use first 2 sections
                actions = section.get('key_actions', [])
                key_actions.extend(actions[:2])  # Take top 2 actions per section
            
            if key_actions:
                return f"This bill {', '.join(key_actions[:3])}."
        
        return "Detailed bill analysis available."
    
    def _extract_affected_parties(self, primary_section: Dict, additional_details: Dict) -> str:
        """Extract who is affected from analysis content"""
        parties = []
        
        if primary_section and primary_section.get('affected_parties'):
            parties.extend(primary_section['affected_parties'])
        
        # Check additional details for stakeholders
        if additional_details:
            stakeholders = additional_details.get('stakeholders', [])
            if isinstance(stakeholders, list):
                parties.extend(stakeholders)
            elif isinstance(stakeholders, str):
                parties.append(stakeholders)
        
        if parties:
            return '; '.join(parties[:4])  # Top 4 affected parties
        
        return "Multiple stakeholders and affected parties"
    
    def _extract_why_matters(self, primary_section: Dict, complete_analysis: List[Dict]) -> str:
        """Extract why this bill matters from analysis content"""
        if primary_section:
            # Look for significance indicators
            significance = primary_section.get('significance') or primary_section.get('importance')
            if significance:
                return significance
            
            # Try policy implications
            implications = primary_section.get('policy_implications')
            if implications:
                return implications
        
        # Build from complete analysis
        if complete_analysis:
            # Count substantive sections
            substantive_sections = [s for s in complete_analysis if s.get('title') and len(s.get('description', '')) > 50]
            section_count = len(substantive_sections)
            
            if section_count > 0:
                topics = [s.get('title', '') for s in substantive_sections[:3]]
                topic_list = ', '.join(topics)
                return f"This legislation addresses {section_count} major areas including {topic_list}."
        
        return "This legislation addresses important policy areas requiring public attention."
    
    def _extract_budget_impact(self, primary_section: Dict, additional_details: Dict) -> str:
        """Extract budget impact from analysis content"""
        # Check primary section for budget info
        if primary_section:
            budget_info = (primary_section.get('budget_impact') or 
                          primary_section.get('fiscal_impact') or 
                          primary_section.get('cost_analysis'))
            if budget_info:
                return budget_info
        
        # Check additional details
        if additional_details:
            funding = additional_details.get('funding_sources')
            if funding:
                if isinstance(funding, list) and funding:
                    return f"Funding sources include: {', '.join(funding[:3])}"
                elif isinstance(funding, str):
                    return funding
            
            # Check for cost estimates
            cost_estimate = additional_details.get('cost_estimate')
            if cost_estimate:
                return f"Estimated cost: {cost_estimate}"
        
        return "Budget and fiscal impact analysis included in detailed provisions."


# Convenience function for easy import
def get_ai_service() -> AIService:
    """Get an AI service instance"""
    return AIService()
