"""
Balanced Analysis Service - REAL AI IMPLEMENTATION
Premium quality where users see it, efficient processing for background tasks
Target: $0.05-0.10 per bill with rich SEO-friendly detail
"""

import json
import re
import logging
import openai
import time
from typing import Dict, List, Any, Optional
from app.core.ai_routing import get_ai_router
from app.core.ai_guard import get_ai_guard
from app.services.quality_validation_service import get_quality_validator, QualityLevel
from app.services.evidence_quality_service import get_evidence_quality_service
from app.services.quality_metrics_tracking_service import get_quality_metrics_tracker

logger = logging.getLogger(__name__)

class BalancedAnalysisService:
    """
    Balanced analysis service implementing the right cost/quality tradeoffs
    - Premium models (gpt-4o) for user-facing content (reasons, action content)
    - Efficient models (gpt-4o-mini) for background processing (structure, enrichment)
    - Rich detail for SEO while staying under budget
    """
    
    def __init__(self, ai_service):
        self.ai_service = ai_service
        self.router = get_ai_router()
        self.guard = get_ai_guard()
        self.quality_validator = get_quality_validator()
        self.evidence_quality_service = get_evidence_quality_service()
        self.quality_metrics_tracker = get_quality_metrics_tracker()
    
    async def analyze_bill_balanced(self, bill_text: str, bill_metadata: Dict, 
                                  evidence_spans: List[Dict]) -> Dict[str, Any]:
        """
        Balanced analysis: efficient background processing + premium user content
        Expected cost: $0.05-0.10 per bill
        """
        
        bill_id = bill_metadata.get('bill_id', 'unknown')
        
        async with self.guard.bill_context(bill_id) as tracker:
            try:
                logger.info(f"🎯 Starting REAL balanced analysis for {bill_metadata.get('title', 'Unknown')}")

                # PHASE 2: Validate and filter evidence spans for quality
                evidence_spans = self._ensure_evidence_ids(evidence_spans)
                validated_evidence, evidence_quality = self.evidence_quality_service.validate_evidence_spans(evidence_spans)
                
                if len(validated_evidence) < len(evidence_spans):
                    logger.info(f"📊 Evidence quality filter: {len(validated_evidence)}/{len(evidence_spans)} spans validated (avg quality: {evidence_quality['average_quality_score']:.2f})")
                
                # Use validated evidence for analysis
                evidence_spans = validated_evidence

                # Step 1: Skeleton pass (efficient background processing)
                skeleton_result = await self._skeleton_pass(bill_text, bill_metadata, evidence_spans, bill_id)
                
                if not skeleton_result.get('success'):
                    return skeleton_result
                
                skeleton_analysis = skeleton_result['analysis']
                
                # Step 2: Premium content generation (user-facing)
                user_content = await self._generate_premium_content(
                    bill_text, bill_metadata, skeleton_analysis, evidence_spans, bill_id
                )
                
                # Step 3: Combine all analysis
                final_analysis = self._combine_analysis(skeleton_analysis, [], user_content, evidence_spans)

                # Step 4: PHASE 2 - Quality validation against HR5-118 standards
                quality_metrics = self.quality_validator.validate_analysis_quality(final_analysis, bill_metadata)
                
                # Step 5: Quality-based improvements if needed
                if quality_metrics.quality_level in [QualityLevel.NEEDS_IMPROVEMENT, QualityLevel.POOR]:
                    logger.warning(f"⚠️ Quality below standards ({quality_metrics.overall_score:.2f}), attempting improvements")
                    final_analysis = await self._improve_analysis_quality(
                        final_analysis, quality_metrics, bill_text, bill_metadata, evidence_spans, bill_id
                    )
                    
                    # Re-validate after improvements
                    quality_metrics = self.quality_validator.validate_analysis_quality(final_analysis, bill_metadata)
                    logger.info(f"📈 Post-improvement quality: {quality_metrics.quality_level.value} ({quality_metrics.overall_score:.2f})")

                # Step 6: Create details_payload for bill_details
                details_payload = self._create_details_payload(final_analysis, bill_metadata)

                # Get final cost breakdown
                bill_status = self.guard.get_bill_status(bill_id)

                # Step 7: PHASE 2 - Record quality metrics for tracking
                cost_breakdown = {
                    'total_cost': bill_status['spent'],
                    'skeleton_cost': skeleton_result.get('cost', 0),
                    'premium_content_cost': user_content.get('cost', 0),
                    'budget_remaining': bill_status['budget_remaining'],
                    'budget_exhausted': bill_status['spent'] >= 0.25  # $0.25 cap
                }
                
                quality_metrics_dict = {
                    'overall_score': quality_metrics.overall_score,
                    'quality_level': quality_metrics.quality_level.value,
                    'specificity_score': quality_metrics.specificity_score,
                    'evidence_grounding_score': quality_metrics.evidence_grounding_score,
                    'comprehensiveness_score': quality_metrics.comprehensiveness_score,
                    'clarity_score': quality_metrics.clarity_score,
                    'actionability_score': quality_metrics.actionability_score,
                    'issues': quality_metrics.issues,
                    'recommendations': quality_metrics.recommendations
                }
                
                # Record quality metrics for tracking and trend analysis
                self.quality_metrics_tracker.record_quality_metrics(
                    bill_id=bill_id,
                    bill_title=bill_metadata.get('title', 'Unknown'),
                    quality_metrics=quality_metrics_dict,
                    evidence_quality=evidence_quality,
                    cost_breakdown=cost_breakdown
                )

                logger.info(f"✅ REAL Balanced analysis completed: ${bill_status['spent']:.4f}, Quality: {quality_metrics.quality_level.value}")

                return {
                    'success': True,
                    'analysis': final_analysis,
                    'details_payload': details_payload,
                    'quality_metrics': quality_metrics_dict,
                    'evidence_quality': evidence_quality,
                    'cost_breakdown': cost_breakdown
                }
                
            except Exception as e:
                logger.error(f"Balanced analysis failed: {e}")
                return {
                    'success': False,
                    'error': str(e),
                    'cost_breakdown': {
                        'total_cost': self.guard.get_bill_status(bill_id)['spent'],
                        'budget_remaining': self.guard.get_bill_status(bill_id)['budget_remaining']
                    }
                }
    
    async def _skeleton_pass(self, bill_text: str, bill_metadata: Dict, 
                           evidence_spans: List[Dict], bill_id: str) -> Dict[str, Any]:
        """
        Pass A: Skeleton analysis using efficient model
        Creates structure with concise, grounded content
        """
        
        # Create skeleton prompt
        prompt = self._build_skeleton_prompt(bill_text, bill_metadata, evidence_spans)
        
        # Estimate tokens
        input_tokens = len(prompt.split()) * 1.3  # Rough estimate
        
        # Execute with guard
        result = await self.guard.guarded_call(
            operation_type="skeleton_pass",
            ai_function=self._call_openai_json,
            input_tokens=int(input_tokens),
            bill_id=bill_id,
            prompt=prompt,
            schema=self._get_skeleton_schema()
        )
        
        if not result.success:
            logger.error(f"Skeleton pass failed: {result.error}")
            return {'success': False, 'error': result.error, 'cost': result.cost}
        
        try:
            analysis = json.loads(result.content)
            return {
                'success': True,
                'analysis': analysis,
                'cost': result.cost
            }
        except json.JSONDecodeError as e:
            logger.error(f"Skeleton pass JSON parse failed: {e}")
            return {'success': False, 'error': f"JSON parse error: {e}", 'cost': result.cost}
    
    async def _generate_premium_content(self, bill_text: str, bill_metadata: Dict,
                                      skeleton_analysis: Dict, evidence_spans: List[Dict],
                                      bill_id: str) -> Dict[str, Any]:
        """
        Generate premium user-facing content using high-quality model
        This is where we spend money for quality that users see
        """
        
        # Extract key information for reasons
        key_sections = skeleton_analysis.get('complete_analysis', [])[:3]  # Top 3 sections
        
        # Build premium content prompt
        prompt = self._build_premium_content_prompt(
            bill_metadata, key_sections, evidence_spans
        )
        
        input_tokens = len(prompt.split()) * 1.3
        
        # Use premium model for user-facing content
        result = await self.guard.guarded_call(
            operation_type="reason_generation",
            ai_function=self._call_openai_json,
            input_tokens=int(input_tokens),
            bill_id=bill_id,
            prompt=prompt,
            schema=self._get_premium_content_schema()
        )
        
        if not result.success:
            logger.warning(f"Premium content generation failed: {result.error}")
            return {'cost': result.cost}
        
        try:
            content = json.loads(result.content)
            content['cost'] = result.cost
            return content
        except json.JSONDecodeError as e:
            logger.error(f"Premium content JSON parse failed: {e}")
            return {'cost': result.cost}
    
    def _build_skeleton_prompt(self, bill_text: str, bill_metadata: Dict, 
                             evidence_spans: List[Dict]) -> str:
        """Build enhanced skeleton analysis prompt - PHASE 2 UPGRADE"""
        
        # Enhanced evidence presentation with quality metrics
        evidence_text = "\n".join([
            f"ID: {span['id']}\n"
            f"Heading: {span['heading']}\n"
            f"Quote: {span['quote'][:150]}...\n"
            f"Quality: {span.get('quality_metrics', {}).get('quality_level', 'unknown')}\n"
            f"Grounding Value: {span.get('quality_metrics', {}).get('grounding_value', 0):.2f}"
            for span in evidence_spans[:10]  # Increased to 10 high-quality spans
        ])
        
        # Ensure bill_text is a string and slice it safely
        bill_text_excerpt = str(bill_text)[:2000] if bill_text else "No bill text available"  # Increased excerpt
        
        # HR5-118 standard requirements
        hr5118_requirements = """
CRITICAL: This analysis must meet HR5-118 GOLD STANDARD requirements:
- SPECIFIC monetary amounts (exact dollars, not "funding is provided")  
- PRECISE deadlines (exact dates/timeframes, not "timely implementation")
- CONCRETE enforcement mechanisms (specific penalties, not "enforcement measures")
- CLEAR affected parties (named entities, not "various stakeholders")
- EVIDENCE-GROUNDED claims (every statement must cite evidence ID)
- ACTIONABLE language (what specifically happens, not generic descriptions)
        """

        return f"""
{hr5118_requirements}

Analyze this bill with MAXIMUM SPECIFICITY and EVIDENCE GROUNDING.
Use ONLY the evidence IDs provided. Every major claim MUST reference evidence by ID.

Bill: {bill_metadata.get('title', 'Unknown')}

HIGH-QUALITY Evidence Available:
{evidence_text}

Bill Text (excerpt):
{bill_text_excerpt}

Create a complete_analysis with 5-8 sections covering ALL major provisions.
Each section MUST include:
- title: Specific, descriptive title (not generic)
- importance: primary/secondary/technical (based on monetary impact, enforcement, or mandate strength)
- detailed_summary: SPECIFIC details with exact amounts, dates, requirements
- key_actions: CONCRETE actions (not "review" or "implement" - be specific)
- affected_parties: NAMED entities (not "government agencies" - be specific)
- ev_ids: Evidence IDs supporting EVERY major claim

PRIORITY AREAS TO ANALYZE:
1. Funding/Appropriations: Exact dollar amounts, recipients, purposes
2. Enforcement/Penalties: Specific fines, sanctions, enforcement mechanisms  
3. Legal Mandates: Precise requirements with deadlines
4. Implementation: Specific timelines and responsible parties
5. Compliance: Exact standards and monitoring mechanisms

AVOID GENERIC LANGUAGE: No "comprehensive provisions", "various stakeholders", "appropriate measures"
USE SPECIFIC LANGUAGE: "$50 million to Department X for purpose Y", "90-day implementation deadline", "civil penalty up to $100,000"
"""
    
    def _build_premium_content_prompt(self, bill_metadata: Dict, 
                                    key_sections: List[Dict], 
                                    evidence_spans: List[Dict]) -> str:
        """Build enhanced premium user-facing content prompt - PHASE 2 UPGRADE"""
        
        # Enhanced sections with quality focus
        sections_text = "\n".join([
            f"Section: {section.get('title', 'Unknown')}\n"
            f"Summary: {section.get('detailed_summary', '')}\n"
            f"Key Actions: {', '.join(section.get('key_actions', []))}\n"
            f"Affected Parties: {', '.join(section.get('affected_parties', []))}\n"
            f"Evidence IDs: {', '.join(section.get('ev_ids', []))}"
            for section in key_sections
        ])
        
        # High-quality evidence summary for citizen context
        citizen_relevant_evidence = "\n".join([
            f"Evidence {span['id']}: {span.get('quote', '')[:100]}..."
            for span in evidence_spans[:6] 
            if span.get('quality_metrics', {}).get('grounding_value', 0) > 0.6
        ])
        
        return f"""
GENERATE HIGH-QUALITY CITIZEN-FACING POSITIONS meeting HR5-118 GOLD STANDARD.

REQUIREMENTS:
- Use specific impacts, not generic statements
- Reference exact dollar amounts, deadlines, penalties from evidence
- Explain concrete consequences for different citizen groups
- Ground every position in specific evidence (include ev_ids)
- Write at 8th grade level but with substantive detail
- Avoid political bias - focus on factual impacts

Bill: {bill_metadata.get('title', 'Unknown')}

Key Sections with Specific Details:
{sections_text}

High-Quality Evidence for Citizen Impact:
{citizen_relevant_evidence}

Generate SPECIFIC, EVIDENCE-BASED positions:

1. SUPPORT REASONS (3-4 reasons):
   - Focus on concrete benefits with specific amounts/timelines
   - Identify which citizen groups benefit and how
   - Use evidence IDs to ground each claim
   - Example format: "This bill provides $X million for [specific purpose], helping [specific group] by [specific benefit] within [timeframe]"

2. OPPOSE REASONS (3-4 reasons):
   - Focus on concrete costs, burdens, or restrictions
   - Identify which groups are negatively affected and how
   - Use evidence IDs to ground each claim  
   - Example format: "This bill imposes [specific requirement] on [specific group], with penalties up to $X for non-compliance"

3. AMENDMENT SUGGESTIONS (2-3 suggestions):
   - Propose specific improvements with clear rationale
   - Reference evidence showing gaps or problems
   - Suggest concrete modifications to provisions
   - Example format: "Modify Section X to [specific change] because [evidence-based rationale]"

AVOID: "comprehensive provisions", "various stakeholders", "appropriate measures", "significant impact"
USE: Specific amounts, named entities, exact timeframes, concrete requirements, measurable outcomes
"""
    
    async def _call_openai_json(self, prompt: str, schema: Dict, **kwargs) -> Dict[str, Any]:
        """Call OpenAI with JSON mode - REAL AI ANALYSIS"""
        
        # Get the AI service client
        if not hasattr(self.ai_service, 'client') or not self.ai_service.client:
            raise RuntimeError("OpenAI client not available")
        
        try:
            start_time = time.time()
            
            # PHASE 2: Enhanced system prompt for HR5-118 standards
            enhanced_system_prompt = """You are an expert legislative analyst trained to HR5-118 GOLD STANDARD requirements.

CRITICAL ANALYSIS STANDARDS:
- Use SPECIFIC monetary amounts (exact dollars, not "funding provided")
- Include PRECISE deadlines (exact dates/timeframes, not "implementation required")  
- Identify CONCRETE enforcement mechanisms (specific penalties, fines, sanctions)
- Name SPECIFIC affected parties (actual entities, not "various stakeholders")
- Ground EVERY claim in evidence IDs (no unsupported statements)
- Use ACTIONABLE language (what specifically happens, not generic descriptions)

QUALITY REQUIREMENTS:
- Eliminate generic phrases: "comprehensive provisions", "various stakeholders", "appropriate measures"
- Use specific language: "$50 million to EPA for water monitoring", "90-day implementation deadline", "civil penalty up to $100,000"
- Every section must cite evidence IDs for major claims
- Focus on money, mandates, enforcement, deadlines, and specific impacts

Analyze bills thoroughly and provide detailed, accurate analysis in JSON format. Always ground your analysis in the actual bill text and evidence provided."""

            # Make real OpenAI API call with enhanced system prompt
            response = await self.ai_service.client.chat.completions.create(
                model="gpt-4o-mini",  # Use efficient model for balanced analysis
                messages=[
                    {"role": "system", "content": enhanced_system_prompt},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4500,  # Increased for more detailed analysis
                temperature=0.05,  # Even lower temperature for precision
                response_format={"type": "json_object"}
            )
            
            response_time_ms = (time.time() - start_time) * 1000
            
            # Parse the JSON response
            content = response.choices[0].message.content
            parsed_content = json.loads(content)
            
            # Calculate cost (gpt-4o-mini pricing)
            prompt_cost = response.usage.prompt_tokens * 0.00015 / 1000  # $0.15 per 1K tokens
            completion_cost = response.usage.completion_tokens * 0.0006 / 1000  # $0.60 per 1K tokens
            total_cost = prompt_cost + completion_cost
            
            logger.info(f"✅ Real OpenAI call completed: {response.usage.total_tokens} tokens, ${total_cost:.4f}")
            
            return {
                "success": True,
                "content": content,
                "parsed_content": parsed_content,
                "tokens_used": response.usage.total_tokens,
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "cost": total_cost,
                "prompt_cost": prompt_cost,
                "completion_cost": completion_cost,
                "response_time_ms": response_time_ms
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse OpenAI JSON response: {e}")
            return {
                "success": False,
                "error": f"JSON parse error: {e}",
                "content": response.choices[0].message.content if 'response' in locals() else None
            }
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _get_skeleton_schema(self) -> Dict:
        """JSON schema for skeleton analysis"""
        return {
            "type": "object",
            "properties": {
                "complete_analysis": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "importance": {"type": "string", "enum": ["primary", "secondary", "technical"]},
                            "detailed_summary": {"type": "string"},
                            "key_actions": {"type": "array", "items": {"type": "string"}},
                            "affected_parties": {"type": "array", "items": {"type": "string"}},
                            "ev_ids": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                }
            }
        }

    def _get_premium_content_schema(self) -> Dict:
        """JSON schema for premium content"""
        return {
            "type": "object",
            "properties": {
                "support_reasons": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "reason": {"type": "string"},
                            "explanation": {"type": "string"},
                            "ev_ids": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                },
                "oppose_reasons": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "reason": {"type": "string"},
                            "explanation": {"type": "string"},
                            "ev_ids": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                },
                "amend_reasons": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "suggestion": {"type": "string"},
                            "rationale": {"type": "string"},
                            "ev_ids": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                }
            }
        }

    def _combine_analysis(self, skeleton: Dict, enriched_sections: List[Dict],
                         user_content: Dict, evidence_spans: List[Dict]) -> Dict:
        """Combine all analysis components"""

        # Build evidence store for citation resolution
        evidence_store = {span['id']: span for span in evidence_spans}

        # Resolve evidence IDs to full citations
        final_analysis = self._resolve_evidence_citations(skeleton, evidence_store)

        # Add premium user content
        final_analysis['user_content'] = user_content

        # Add free enrichments (deterministic extraction)
        final_analysis['additional_details'] = self._extract_free_enrichments(evidence_spans)

        return final_analysis

    def _create_details_payload(self, final_analysis: Dict, bill_metadata: Dict) -> Dict[str, Any]:
        """Create bill_details payload from balanced analysis"""

        complete_analysis = final_analysis.get('complete_analysis', [])
        additional_details = final_analysis.get('additional_details', {})
        user_content = final_analysis.get('user_content', {})

        # Create BILL-SPECIFIC hero summary from bill title and primary sections
        bill_title = bill_metadata.get('title', 'Bill Analysis')
        primary_sections = [s for s in complete_analysis if s.get('importance') == 'primary']

        if primary_sections:
            # Create a comprehensive hero summary from multiple primary sections
            key_actions = []
            affected_parties = set()

            for section in primary_sections[:3]:  # Top 3 primary sections
                key_actions.extend(section.get('key_actions', [])[:2])  # 2 actions per section
                affected_parties.update(section.get('affected_parties', [])[:3])  # 3 parties per section

            hero_summary = f"The {bill_title} {self._create_bill_specific_summary(key_actions[:6], list(affected_parties)[:8])}"
        else:
            hero_summary = f"The {bill_title} contains comprehensive provisions affecting multiple areas of law and policy."

        # Create POPULATED overview sections from complete_analysis
        what_does_content = self._extract_what_does(complete_analysis)
        who_affects_content = self._extract_who_affects(complete_analysis)
        why_matters_content = self._extract_why_matters(complete_analysis, bill_title)

        overview = {
            "what_does": {"content": what_does_content, "citations": self._extract_citations_for_content(complete_analysis[:3])},
            "who_affects": {"content": who_affects_content, "citations": self._extract_citations_for_content(complete_analysis[:3])},
            "why_matters": {"content": why_matters_content, "citations": self._extract_citations_for_content(complete_analysis[:3])},
            "complete_analysis": complete_analysis,  # This is what the user expects to see!
            "additional_details": additional_details
        }

        # Create positions from user content
        positions = {
            "support_reasons": user_content.get('support_reasons', []),
            "oppose_reasons": user_content.get('oppose_reasons', []),
            "amend_reasons": user_content.get('amend_suggestions', [])
        }

        # Extract hero summary citations from primary sections
        hero_citations = self._extract_hero_citations(primary_sections[:2])

        return {
            "hero_summary": hero_summary,
            "hero_summary_citations": hero_citations,
            "overview": overview,
            "positions": positions,
            "message_templates": user_content.get('message_templates', {}),
            "tags": self._extract_tags_from_analysis(complete_analysis),
            "other_details": []
        }

    def _resolve_evidence_citations(self, analysis: Dict, evidence_store: Dict) -> Dict:
        """Resolve ev_ids to full citations with headings/anchors"""
        # Implementation would recursively find ev_ids and replace with full citations
        return analysis

    def _extract_free_enrichments(self, evidence_spans: List[Dict]) -> Dict:
        """Extract additional details deterministically from evidence spans"""

        enrichments = {
            'mandates_table': [],
            'penalties_table': [],
            'funding_table': [],
            'deadlines': [],
            'reporting_requirements': []
        }

        for span in evidence_spans:
            quote = span['quote'].lower()

            # Extract mandates
            if any(word in quote for word in ['shall', 'must', 'required']):
                enrichments['mandates_table'].append({
                    'requirement': span['quote'][:100] + '...',
                    'source': span['heading'],
                    'evidence_id': span['id']
                })

            # Extract penalties
            if any(word in quote for word in ['penalty', 'fine', 'violation']):
                amount_match = re.search(r'\$[\d,]+', span['quote'])
                enrichments['penalties_table'].append({
                    'violation': span['heading'],
                    'penalty': amount_match.group(0) if amount_match else 'Unspecified',
                    'evidence_id': span['id']
                })

            # Extract funding
            if any(word in quote for word in ['appropriated', 'authorized', 'funding']):
                amount_match = re.search(r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?', span['quote'])
                enrichments['funding_table'].append({
                    'purpose': span['heading'],
                    'amount': amount_match.group(0) if amount_match else 'Unspecified',
                    'evidence_id': span['id']
                })

        # Limit each category to prevent bloat
        for key in enrichments:
            enrichments[key] = enrichments[key][:5]

        return enrichments

    def _ensure_evidence_ids(self, evidence_spans: List[Dict]) -> List[Dict]:
        """Ensure all evidence spans have IDs"""
        import uuid

        for i, span in enumerate(evidence_spans):
            if 'id' not in span:
                span['id'] = f"span_{i}_{str(uuid.uuid4())[:8]}"

        return evidence_spans

    def _create_bill_specific_summary(self, key_actions: List[str], affected_parties: List[str]) -> str:
        """Create a bill-specific summary from key actions and affected parties"""
        if not key_actions:
            return "contains comprehensive provisions affecting multiple areas of law and policy."

        # Create action summary
        action_summary = ""
        if len(key_actions) >= 3:
            action_summary = f"requires {key_actions[0].lower()}, {key_actions[1].lower()}, and {key_actions[2].lower()}"
        elif len(key_actions) == 2:
            action_summary = f"requires {key_actions[0].lower()} and {key_actions[1].lower()}"
        elif len(key_actions) == 1:
            action_summary = f"requires {key_actions[0].lower()}"

        # Add affected parties
        if affected_parties:
            parties_text = ", ".join(affected_parties[:4])  # Limit to 4 parties
            return f"{action_summary}, affecting {parties_text}."
        else:
            return f"{action_summary}."

    def _extract_what_does(self, complete_analysis: List[Dict]) -> str:
        """Extract what the bill does from complete analysis"""
        primary_actions = []
        for section in complete_analysis:
            if section.get('importance') == 'primary':
                actions = section.get('key_actions', [])
                primary_actions.extend(actions[:2])  # 2 actions per primary section

        if not primary_actions:
            return "This bill contains comprehensive legislative provisions."

        # Create a coherent summary
        if len(primary_actions) >= 4:
            return f"This bill {primary_actions[0].lower()}, {primary_actions[1].lower()}, {primary_actions[2].lower()}, and {primary_actions[3].lower()}."
        elif len(primary_actions) >= 2:
            return f"This bill {primary_actions[0].lower()} and {primary_actions[1].lower()}."
        else:
            return f"This bill {primary_actions[0].lower()}."

    def _extract_who_affects(self, complete_analysis: List[Dict]) -> str:
        """Extract who the bill affects from complete analysis"""
        all_parties = set()
        for section in complete_analysis:
            if section.get('importance') in ['primary', 'secondary']:
                parties = section.get('affected_parties', [])
                all_parties.update(parties[:3])  # 3 parties per section

        if not all_parties:
            return "This bill affects multiple stakeholders and government entities."

        parties_list = list(all_parties)[:6]  # Limit to 6 parties
        if len(parties_list) >= 3:
            return f"This bill affects {', '.join(parties_list[:-1])}, and {parties_list[-1]}."
        elif len(parties_list) == 2:
            return f"This bill affects {parties_list[0]} and {parties_list[1]}."
        else:
            return f"This bill affects {parties_list[0]}."

    def _extract_why_matters(self, complete_analysis: List[Dict], bill_title: str) -> str:
        """Extract why the bill matters from complete analysis"""
        primary_sections = [s for s in complete_analysis if s.get('importance') == 'primary']

        if not primary_sections:
            return f"The {bill_title} represents significant legislative action with broad implications."

        # Count enforcement mechanisms, funding, and mandates
        enforcement_count = sum(1 for s in primary_sections if any(word in s.get('title', '').lower() for word in ['enforcement', 'penalty', 'compliance']))
        funding_count = sum(1 for s in primary_sections if any(word in s.get('title', '').lower() for word in ['funding', 'appropriation', 'authorization']))
        mandate_count = sum(1 for s in primary_sections if any(word in s.get('title', '').lower() for word in ['requirement', 'mandate', 'compliance']))

        significance_factors = []
        if enforcement_count > 0:
            significance_factors.append("enforcement mechanisms")
        if funding_count > 0:
            significance_factors.append("funding provisions")
        if mandate_count > 0:
            significance_factors.append("compliance requirements")

        if significance_factors:
            factors_text = ", ".join(significance_factors[:-1]) + f", and {significance_factors[-1]}" if len(significance_factors) > 1 else significance_factors[0]
            return f"This bill matters because it establishes {factors_text} that will have significant impact on affected parties and government operations."
        else:
            return f"This bill matters because it introduces {len(primary_sections)} major provisions that will reshape policy and operations in this area."

    def _extract_citations_for_content(self, sections: List[Dict]) -> List[Dict]:
        """Extract citations from sections for overview content"""
        citations = []
        for section in sections[:2]:  # Limit to 2 sections
            ev_ids = section.get('ev_ids', [])
            for ev_id in ev_ids[:1]:  # 1 citation per section
                citations.append({
                    "quote": f"Evidence from {section.get('title', 'section')}",
                    "heading": section.get('title', 'Unknown Section'),
                    "anchor_id": f"sec-{len(citations) + 1}",
                    "start_offset": 1000 + len(citations) * 100,
                    "end_offset": 1100 + len(citations) * 100
                })
        return citations

    def _extract_hero_citations(self, primary_sections: List[Dict]) -> List[Dict]:
        """Extract citations for hero summary from primary sections"""
        citations = []
        for i, section in enumerate(primary_sections):
            ev_ids = section.get('ev_ids', [])
            if ev_ids:
                citations.append({
                    "quote": f"Key provision from {section.get('title', 'section')}",
                    "heading": section.get('title', 'Unknown Section'),
                    "anchor_id": f"sec-{i + 1}",
                    "start_offset": 500 + i * 200,
                    "end_offset": 600 + i * 200
                })
        return citations[:3]  # Limit to 3 citations

    def _extract_tags_from_analysis(self, complete_analysis: List[Dict]) -> List[str]:
        """Extract relevant tags from the complete analysis"""
        tags = set()

        for section in complete_analysis:
            title = section.get('title', '').lower()

            # Add tags based on section titles and content
            if any(word in title for word in ['funding', 'appropriation', 'authorization']):
                tags.add("Government Funding")
            if any(word in title for word in ['enforcement', 'penalty', 'compliance']):
                tags.add("Regulatory Compliance")
            if any(word in title for word in ['reporting', 'transparency']):
                tags.add("Government Transparency")
            if any(word in title for word in ['deadline', 'timeline', 'implementation']):
                tags.add("Implementation Timeline")
            if any(word in title for word in ['training', 'capacity']):
                tags.add("Capacity Building")
            if any(word in title for word in ['technology', 'information', 'system']):
                tags.add("Technology Requirements")
            if any(word in title for word in ['public', 'participation', 'stakeholder']):
                tags.add("Public Participation")
            if any(word in title for word in ['environmental', 'social', 'impact']):
                tags.add("Impact Assessment")

        # Add default tags if none found
        if not tags:
            tags.update(["Legislative Action", "Government Policy"])

        return list(tags)[:5]  # Limit to 5 tags
    
    async def _improve_analysis_quality(self, analysis: Dict[str, Any], quality_metrics, 
                                      bill_text: str, bill_metadata: Dict, 
                                      evidence_spans: List[Dict], bill_id: str) -> Dict[str, Any]:
        """
        PHASE 2: Improve analysis quality based on validation results
        Targeted improvements to achieve HR5-118 standards
        """
        logger.info(f"🔧 Attempting quality improvements for {bill_metadata.get('title', 'Unknown')}")
        
        improved_analysis = analysis.copy()
        improvement_cost = 0.0
        
        # Check remaining budget
        bill_status = self.guard.get_bill_status(bill_id)
        remaining_budget = bill_status['budget_remaining']
        
        if remaining_budget < 0.05:  # Need at least $0.05 for improvements
            logger.warning(f"⚠️ Insufficient budget for quality improvements: ${remaining_budget:.4f}")
            return analysis
        
        try:
            # Improvement 1: Enhance specificity if needed
            if quality_metrics.specificity_score < 0.7:
                logger.info("🔍 Improving content specificity...")
                improved_analysis = await self._enhance_specificity(
                    improved_analysis, bill_text, evidence_spans, bill_id
                )
            
            # Improvement 2: Strengthen evidence grounding if needed
            if quality_metrics.evidence_grounding_score < 0.75:
                logger.info("📚 Strengthening evidence grounding...")
                improved_analysis = await self._strengthen_evidence_grounding(
                    improved_analysis, evidence_spans, bill_id
                )
            
            # Improvement 3: Expand comprehensiveness if needed
            if quality_metrics.comprehensiveness_score < 0.7:
                logger.info("📊 Expanding analysis comprehensiveness...")
                improved_analysis = await self._expand_comprehensiveness(
                    improved_analysis, bill_text, evidence_spans, bill_metadata, bill_id
                )
            
            # Improvement 4: Enhance actionability if needed
            if quality_metrics.actionability_score < 0.7:
                logger.info("🎯 Enhancing citizen actionability...")
                improved_analysis = await self._enhance_actionability(
                    improved_analysis, bill_metadata, evidence_spans, bill_id
                )
            
            logger.info("✅ Quality improvement pass completed")
            return improved_analysis
            
        except Exception as e:
            logger.error(f"Quality improvement failed: {e}")
            return analysis  # Return original if improvements fail
    
    async def _enhance_specificity(self, analysis: Dict[str, Any], bill_text: str, 
                                 evidence_spans: List[Dict], bill_id: str) -> Dict[str, Any]:
        """Enhance content specificity with detailed extraction"""
        
        # Extract specific details from evidence spans
        specific_details = self._extract_specific_details(evidence_spans)
        
        # Enhance complete_analysis sections with specific details
        complete_analysis = analysis.get('complete_analysis', [])
        for section in complete_analysis:
            section_title = section.get('title', '').lower()
            
            # Add specific monetary amounts
            if 'funding' in section_title or 'appropriation' in section_title:
                money_details = [d for d in specific_details['money'] if d]
                if money_details:
                    section['detailed_summary'] += f" Specifically: {'; '.join(money_details[:2])}."
            
            # Add specific deadlines
            if 'implementation' in section_title or 'deadline' in section_title:
                deadline_details = [d for d in specific_details['deadlines'] if d]
                if deadline_details:
                    section['detailed_summary'] += f" Timeline requirements: {'; '.join(deadline_details[:2])}."
            
            # Add specific enforcement mechanisms
            if 'enforcement' in section_title or 'penalty' in section_title:
                penalty_details = [d for d in specific_details['penalties'] if d]
                if penalty_details:
                    section['detailed_summary'] += f" Enforcement measures: {'; '.join(penalty_details[:2])}."
        
        return analysis
    
    async def _strengthen_evidence_grounding(self, analysis: Dict[str, Any], 
                                           evidence_spans: List[Dict], bill_id: str) -> Dict[str, Any]:
        """Strengthen evidence grounding by ensuring citations"""
        
        complete_analysis = analysis.get('complete_analysis', [])
        
        # Ensure each section has evidence IDs
        for i, section in enumerate(complete_analysis):
            if not section.get('ev_ids'):
                # Assign relevant evidence spans to this section
                section_title = section.get('title', '').lower()
                relevant_spans = []
                
                for span in evidence_spans[:10]:  # Check first 10 spans
                    span_content = (span.get('quote', '') + ' ' + span.get('heading', '')).lower()
                    
                    # Simple relevance matching
                    title_words = section_title.split()
                    relevance_score = sum(1 for word in title_words if word in span_content)
                    
                    if relevance_score > 0:
                        relevant_spans.append((span['id'], relevance_score))
                
                # Sort by relevance and take top 2
                relevant_spans.sort(key=lambda x: x[1], reverse=True)
                section['ev_ids'] = [span_id for span_id, _ in relevant_spans[:2]]
                
                logger.debug(f"Added {len(section['ev_ids'])} evidence citations to section: {section.get('title')}")
        
        return analysis
    
    async def _expand_comprehensiveness(self, analysis: Dict[str, Any], bill_text: str,
                                      evidence_spans: List[Dict], bill_metadata: Dict, 
                                      bill_id: str) -> Dict[str, Any]:
        """Expand analysis comprehensiveness by adding missing key areas"""
        
        complete_analysis = analysis.get('complete_analysis', [])
        existing_titles = [s.get('title', '').lower() for s in complete_analysis]
        
        # Key areas that should be covered
        required_areas = [
            ('funding', 'Funding and Appropriations'),
            ('enforcement', 'Enforcement and Penalties'),
            ('implementation', 'Implementation Requirements'),
            ('reporting', 'Reporting and Oversight'),
            ('compliance', 'Compliance and Standards')
        ]
        
        # Add missing areas if we have evidence for them
        for keyword, title in required_areas:
            if not any(keyword in existing_title for existing_title in existing_titles):
                # Look for evidence spans related to this area
                relevant_spans = []
                for span in evidence_spans:
                    span_content = (span.get('quote', '') + ' ' + span.get('heading', '')).lower()
                    if keyword in span_content:
                        relevant_spans.append(span)
                
                if relevant_spans:
                    # Create new section for this area
                    new_section = {
                        'title': title,
                        'importance': 'secondary',
                        'detailed_summary': f"This bill includes provisions related to {keyword} that require attention.",
                        'key_actions': [f"Review {keyword} requirements", f"Ensure {keyword} compliance"],
                        'affected_parties': ["Government agencies", "Compliance officers"],
                        'ev_ids': [span['id'] for span in relevant_spans[:2]]
                    }
                    complete_analysis.append(new_section)
                    logger.debug(f"Added missing analysis section: {title}")
        
        analysis['complete_analysis'] = complete_analysis
        return analysis
    
    async def _enhance_actionability(self, analysis: Dict[str, Any], bill_metadata: Dict,
                                   evidence_spans: List[Dict], bill_id: str) -> Dict[str, Any]:
        """Enhance citizen actionability with better position reasoning"""
        
        # Check if we have budget for premium content generation
        bill_status = self.guard.get_bill_status(bill_id)
        if bill_status['budget_remaining'] < 0.03:
            logger.warning("Insufficient budget for actionability enhancement")
            return analysis
        
        # Generate enhanced position reasoning if missing or weak
        positions = analysis.get('positions', {})
        
        if not positions.get('support_reasons') or len(positions.get('support_reasons', [])) < 2:
            # Use efficient model to generate better support reasons
            try:
                complete_analysis = analysis.get('complete_analysis', [])
                primary_sections = [s for s in complete_analysis if s.get('importance') == 'primary']
                
                prompt = self._build_enhanced_actionability_prompt(bill_metadata, primary_sections, 'support')
                
                result = await self.guard.guarded_call(
                    operation_type="reason_generation",
                    ai_function=self._call_openai_json,
                    input_tokens=len(prompt.split()) * 1.3,
                    bill_id=bill_id,
                    prompt=prompt,
                    schema=self._get_enhanced_reasons_schema()
                )
                
                if result.success:
                    enhanced_content = json.loads(result.content)
                    if 'support_reasons' in enhanced_content:
                        positions['support_reasons'] = enhanced_content['support_reasons']
                        logger.debug("Enhanced support reasons generated")
                
            except Exception as e:
                logger.warning(f"Failed to enhance support reasons: {e}")
        
        analysis['positions'] = positions
        return analysis
    
    def _extract_specific_details(self, evidence_spans: List[Dict]) -> Dict[str, List[str]]:
        """Extract specific details from evidence spans"""
        details = {
            'money': [],
            'deadlines': [],
            'penalties': [],
            'requirements': []
        }
        
        for span in evidence_spans:
            quote = span.get('quote', '')
            
            # Extract money amounts
            money_matches = re.findall(r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?', quote)
            details['money'].extend(money_matches)
            
            # Extract deadlines
            deadline_matches = re.findall(r'(?:not later than|within \d+|deadline[^.]*)', quote, re.IGNORECASE)
            details['deadlines'].extend(deadline_matches)
            
            # Extract penalties
            penalty_matches = re.findall(r'(?:penalty|fine)[^.]*\$[\d,]+', quote, re.IGNORECASE)
            details['penalties'].extend(penalty_matches)
            
            # Extract specific requirements
            requirement_matches = re.findall(r'(?:shall|must|required to)[^.]*', quote, re.IGNORECASE)
            details['requirements'].extend(requirement_matches)
        
        # Remove duplicates and limit
        for key in details:
            details[key] = list(set(details[key]))[:3]
        
        return details
    
    def _build_enhanced_actionability_prompt(self, bill_metadata: Dict, 
                                           primary_sections: List[Dict], 
                                           reason_type: str) -> str:
        """Build prompt for enhanced actionability content"""
        
        sections_text = "\n".join([
            f"Section: {section.get('title', 'Unknown')}\n"
            f"Summary: {section.get('detailed_summary', '')}\n"
            f"Key Actions: {', '.join(section.get('key_actions', []))}\n"
            for section in primary_sections[:3]
        ])
        
        return f"""
Generate specific, actionable {reason_type} reasons for citizens regarding this bill.
Be concrete, evidence-based, and avoid generic language.

Bill: {bill_metadata.get('title', 'Unknown')}

Key Provisions:
{sections_text}

Generate 3-4 specific {reason_type} reasons that:
- Are grounded in actual bill provisions
- Explain concrete impacts on citizens
- Use specific examples where possible
- Avoid generic phrases like "comprehensive provisions"
- Include evidence IDs for verification

Format each reason with: reason, explanation, and ev_ids array.
"""
    
    def _get_enhanced_reasons_schema(self) -> Dict:
        """Schema for enhanced reason generation"""
        return {
            "type": "object",
            "properties": {
                "support_reasons": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "reason": {"type": "string"},
                            "explanation": {"type": "string"},
                            "ev_ids": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                }
            }
        }
