"""
AI Model Routing - Single Source of Truth
Balanced approach: Premium models for user-facing content, efficient models for background processing
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass
import os

@dataclass
class ModelConfig:
    """Configuration for AI model routing"""
    model: str
    temperature: float
    max_tokens: int
    timeout_ms: int = 15000
    use_json_mode: bool = True

class AIRouter:
    """
    Central router for all AI model decisions
    Keeps premium quality where users see it, optimizes background processing
    """
    
    def __init__(self):
        # Budget controls
        self.bill_cost_cap = float(os.getenv("AI_BILL_COST_CAP_USD", "0.28"))
        self.bill_deadline_ms = int(os.getenv("AI_BILL_DEADLINE_MS", "60000"))  # 60s per bill
        self.call_timeout_ms = int(os.getenv("AI_CALL_TIMEOUT_MS", "15000"))   # 15s per call
        self.enrich_max_sections = int(os.getenv("AI_ENRICH_MAX_SECTIONS", "5"))
        
        # Model selection
        self.reason_model = os.getenv("AI_MODEL_REASON", "gpt-4o")
        self.skeleton_model = os.getenv("AI_MODEL_SKELETON", "gpt-4o-mini")
        self.enrich_model = os.getenv("AI_MODEL_ENRICH", "gpt-4o-mini")
        
        # Route definitions
        self.routes = {
            # USER-FACING: Premium quality where humans feel it
            "reason_generation": ModelConfig(
                model=self.reason_model,
                temperature=0.4,
                max_tokens=450,
                timeout_ms=20000  # Allow more time for quality
            ),
            
            "action_content": ModelConfig(
                model=self.reason_model,
                temperature=0.3,
                max_tokens=400,
                timeout_ms=20000
            ),
            
            # BACKGROUND: Efficient processing for internal use
            "skeleton_pass": ModelConfig(
                model=self.skeleton_model,
                temperature=0.1,
                max_tokens=900,
                timeout_ms=45000  # Increased to 45s for real AI calls
            ),
            
            "detailed_enrichment": ModelConfig(
                model=self.enrich_model,
                temperature=0.2,
                max_tokens=700,
                timeout_ms=15000
            ),
            
            "structure_repair": ModelConfig(
                model=self.skeleton_model,
                temperature=0.0,
                max_tokens=150,
                timeout_ms=10000
            ),
            
            "validator_explain": ModelConfig(
                model=self.skeleton_model,
                temperature=0.0,
                max_tokens=150,
                timeout_ms=10000
            ),
            
            # BATCH PROCESSING: For multiple small sections
            "batch_sections": ModelConfig(
                model=self.skeleton_model,
                temperature=0.1,
                max_tokens=1200,
                timeout_ms=20000
            )
        }
    
    def get_config(self, operation_type: str) -> ModelConfig:
        """Get model configuration for operation type"""
        return self.routes.get(operation_type, self.routes["skeleton_pass"])
    
    def plan_or_block(self, projected_cost: float, spent_so_far: float) -> bool:
        """
        Decide whether to proceed with operation based on budget
        Returns True if operation should proceed
        """
        total_projected = spent_so_far + projected_cost
        
        if total_projected > self.bill_cost_cap:
            return False
        
        # Reserve budget for essential user-facing operations
        reason_reserve = 0.04  # Reserve for reason generation
        remaining_budget = self.bill_cost_cap - spent_so_far - reason_reserve
        
        return projected_cost <= remaining_budget
    
    def estimate_cost(self, operation_type: str, input_tokens: int) -> float:
        """Estimate cost for operation"""
        config = self.get_config(operation_type)
        
        # Cost per 1k tokens (conservative estimates)
        if config.model == "gpt-4o":
            input_cost = 0.0025  # $2.50 per 1M tokens
            output_cost = 0.010   # $10.00 per 1M tokens
        elif config.model == "gpt-4-turbo":
            input_cost = 0.010    # $10.00 per 1M tokens
            output_cost = 0.030   # $30.00 per 1M tokens
        else:  # gpt-4o-mini
            input_cost = 0.00015  # $0.15 per 1M tokens
            output_cost = 0.0006  # $0.60 per 1M tokens
        
        input_cost_total = (input_tokens / 1000) * input_cost
        output_cost_total = (config.max_tokens / 1000) * output_cost
        
        return input_cost_total + output_cost_total
    
    def get_priority_sections(self, sections: list, max_sections: int = None) -> list:
        """
        Prioritize sections for enrichment based on importance
        Money, mandates, enforcement get premium treatment
        """
        if max_sections is None:
            max_sections = self.enrich_max_sections
        
        priority_keywords = [
            # High priority - money and enforcement
            ['appropriat', 'fund', 'budget', 'authorization', 'million', 'billion'],
            ['shall', 'must', 'require', 'mandate', 'compliance'],
            ['enforce', 'penalty', 'fine', 'violation', 'civil penalty'],
            ['deadline', 'not later than', 'within', 'days after'],
            
            # Medium priority - structure and process
            ['definition', 'means', 'term', 'include'],
            ['report', 'study', 'analysis', 'assessment'],
            ['rule', 'regulation', 'guidance', 'standard']
        ]
        
        scored_sections = []
        for section in sections:
            score = 0
            title = section.get('title', '').lower()
            content = section.get('content', '').lower()
            
            # Score based on priority keywords
            for i, keyword_group in enumerate(priority_keywords):
                for keyword in keyword_group:
                    if keyword in title:
                        score += (len(priority_keywords) - i) * 2  # Title matches worth more
                    if keyword in content:
                        score += (len(priority_keywords) - i)
            
            # Boost for importance markers
            importance = section.get('importance', 'technical')
            if importance == 'primary':
                score += 10
            elif importance == 'secondary':
                score += 5
            
            scored_sections.append((section, score))
        
        # Sort by score and return top sections
        scored_sections.sort(key=lambda x: x[1], reverse=True)
        return [section for section, score in scored_sections[:max_sections]]

# Global router instance
ai_router = AIRouter()

def get_ai_router() -> AIRouter:
    """Get the global AI router instance"""
    return ai_router
